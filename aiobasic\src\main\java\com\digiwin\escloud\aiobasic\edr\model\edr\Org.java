package com.digiwin.escloud.aiobasic.edr.model.edr;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiobasic.edr.model.base.OrgBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Org extends OrgBase {
    private long orgEdrId;
    private long organizationId;
    private String name;
    private int workstationsAllocated;
    private int serversAllocated;
    private int iotAllocated;
    private int workstationsInUse;
    private int serversInUse;
    private int iotInUse;
    private int workstationsNotInUse;
    private int serversNotInUse;
    private int iotNotInUse;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expirationDate;
    private boolean expired;
    private boolean expiringSoon; //即将到期
    private String marketUrl; //雲端市場單一登入url
    private boolean enableRenewal; //是否開啟續費
    private int expiredDays;
    private boolean vulnerabilityAndIoT;
    private boolean forensicsAndEDR;
    private String verificationCode;
    private boolean isAdminAccount;
    private JSONObject collectorsState;

    private Integer syncStatus;
    private String voidReason;
    private Date voidTime;
    private String processUserId;
    private String processUserName;
}
