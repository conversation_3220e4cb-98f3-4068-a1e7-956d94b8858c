package com.digiwin.escloud.aiocdp.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocdp.constant.UcdpConst;
import com.digiwin.escloud.aiocdp.industry.model.PageParams;
import com.digiwin.escloud.aiocdp.industry.model.SendMessageConfigParam;
import com.digiwin.escloud.aiocdp.industry.model.SendMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Date: 2025-05-06 11:18
 * @Description
 */
@Slf4j
@Component
@RefreshScope
public class UcdpUtil {

    @Value("${ucdp.api.url}")
    private String ucdpUrl;
    @Resource(name = "restTp")
    private RestTemplate restTemplate;

    public JSONObject sendQyWechatMsg(SendMessageDto messageDto) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        long timestamp = System.currentTimeMillis();
        String sortStr = ObjectSerializeUtils.generateObject(messageDto);
        String md5 = DigestUtils.md5Hex((sortStr + "&timestamp=" + timestamp).getBytes(StandardCharsets.UTF_8));

        headers.set("timestamp", timestamp + "");
        headers.set("signature", md5);

        HttpEntity<SendMessageDto> requestEntity = new HttpEntity<>(messageDto, headers);
        log.info("sendQyWechatMsg request:{}", JSON.toJSONString(requestEntity));
        JSONObject result = restTemplate.postForObject(ucdpUrl + UcdpConst.QY_WECHAT_SEND_MSG_URL, requestEntity, JSONObject.class);
        if (result == null) {
            return null;
        }
        log.info("sendQyWechatMsg result:{}", JSON.toJSONString(result));
        return result;
    }

    public JSONObject sendQyWechatMsgV2(SendMessageConfigParam sendMessageConfigParam) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        long timestamp = System.currentTimeMillis();
        String sortStr = ObjectSerializeUtils.generateObject(sendMessageConfigParam);
        String md5 = DigestUtils.md5Hex((sortStr + "&timestamp=" + timestamp).getBytes(StandardCharsets.UTF_8));

        headers.set("timestamp", timestamp + "");
        headers.set("signature", md5);

        HttpEntity<SendMessageConfigParam> requestEntity = new HttpEntity<>(sendMessageConfigParam, headers);
        log.info("sendQyWechatMsgV2 request:{}", JSON.toJSONString(requestEntity));
        JSONObject result = restTemplate.postForObject(ucdpUrl + UcdpConst.QY_WECHAT_SEND_MSG_URL_V2, requestEntity, JSONObject.class);
        if (result == null) {
            return null;
        }
        log.info("sendQyWechatMsgV2 result:{}", JSON.toJSONString(result));
        return result;
    }

    private HttpHeaders buildHeaders(Object obj) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        long timestamp = System.currentTimeMillis();
        String sortStr = ObjectSerializeUtils.generateObject(obj);
        String md5 = DigestUtils.md5Hex((sortStr + "&timestamp=" + timestamp).getBytes(StandardCharsets.UTF_8));
        headers.set("timestamp", timestamp + "");
        headers.set("signature", md5);
        return headers;
    }

    public JSONArray queryDigiwinStaff(PageParams pageParams) {
        HttpHeaders httpHeaders = buildHeaders(pageParams);
        HttpEntity<PageParams> requestEntity = new HttpEntity<>(pageParams, httpHeaders);
        log.info("queryDigiwinStaff request:{}", JSON.toJSONString(requestEntity));
        JSONObject result = restTemplate.postForObject(ucdpUrl + UcdpConst.EMPLOYEE_LIST_URL, requestEntity, JSONObject.class);
        log.info("queryDigiwinStaff result:{}", JSON.toJSONString(result));
        if (result == null) {
            return null;
        }
        if (result.getInteger("code")!=200) {
            return null;
        }
        JSONObject data = result.getJSONObject("data");
        if (data == null) {
            return null;
        }
        return data.getJSONArray("data");
    }
}
