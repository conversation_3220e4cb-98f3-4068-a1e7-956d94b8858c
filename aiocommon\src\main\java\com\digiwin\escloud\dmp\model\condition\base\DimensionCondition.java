package com.digiwin.escloud.dmp.model.condition.base;

import com.digiwin.escloud.common.model.LogicType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 10:44
 * @Description
 */
@Data
public class DimensionCondition extends BaseCondition {
    @ApiModelProperty("维度id")
    private long dimensionId;
    @ApiModelProperty("维度关联")
    private String linkModelCode;
    @ApiModelProperty("操作符")
    private String operator;
    @ApiModelProperty("值")
    private String operatorValue;
    @ApiModelProperty("值类型")
    private String operatorType;
    @ApiModelProperty("左值")
    private String leftOperatorValue;
    @ApiModelProperty("左值类型")
    private String leftOperatorType;
    @ApiModelProperty("右值")
    private String rightOperatorValue;
    @ApiModelProperty("右值类型")
    private String rightOperatorType;
    @ApiModelProperty("逻辑符")
    private LogicType logic;
    @ApiModelProperty("左括号")
    private String leftBracket;
    @ApiModelProperty("右括号")
    private String rightBracket;
    @ApiModelProperty("序号")
    private int orderNum;
}
