package com.digiwin.escloud.aiochat.aichat.controller;

import com.digiwin.escloud.aiochat.aichat.ChatService;
import com.digiwin.escloud.aiochat.aichat.SseClient;
import com.digiwin.escloud.aiochat.aichat.model.ChatAssistant;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessage;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessageFeedback;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

@RestController
@RequestMapping("/chat")
@Slf4j
public class ChatController {

    @Resource
    private ChatService chatService;

    @Resource
    private SseClient sseClient;

    @PostMapping(value = "/invokeIndepthAIAgent", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<Flux<ServerSentEvent<Object>>> invokeIndepthAIAgent(@RequestBody ChatMessage chatMessage) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Accel-Buffering", "no");
        headers.add("Content-Type", "text/event-stream");
        headers.add("Cache-Control", "no-cache");
        return ResponseEntity.ok()
                .headers(headers)
                .body(chatService.invokeIndepthAIAgent(chatMessage));
    }

    @PostMapping(value = "/saveChatMessageFeedback")
    public ResponseBase saveChatMessageFeedback(@RequestBody ChatMessageFeedback feedback) {
        return chatService.saveChatMessageFeedback(feedback);
    }

    @ApiOperation(value = "根据aiAgentId更新智能体信息")
    @PutMapping(value = "/assistant")
    public ResponseBase updateChatAssistantByAiAgentId(
            @ApiParam(value = "智能体信息", required = true)
            @RequestBody ChatAssistant chatAssistant) {
        return chatService.updateChatAssistantByAiAgentId(chatAssistant);
    }

    @ApiOperation(value = "新增智能体信息")
    @PostMapping(value = "/assistant")
    public ResponseBase insertChatAssistant(
            @ApiParam(value = "智能体信息", required = true)
            @RequestBody ChatAssistant chatAssistant) {
        return chatService.insertChatAssistant(chatAssistant);
    }

}