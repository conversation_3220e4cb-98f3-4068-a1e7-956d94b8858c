package com.digiwin.escloud.dmp.model.condition;

import com.digiwin.escloud.dmp.model.condition.base.DimensionCondition;
import com.digiwin.escloud.dmp.model.taggroup.TagRuleType;
import com.digiwin.escloud.dmp.model.taggroup.ValueType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 10:16
 * @Description
 */
@Data
public class TagCondition extends DimensionCondition {
    @ApiModelProperty("使用标签id")
    private long usedTagId;
    @ApiModelProperty("使用标签类型")
    private TagRuleType usedTagRuleType;
    @ApiModelProperty("值类型")
    private ValueType valueType;
    @ApiModelProperty("存在值")
    private String existValue;
}
