package com.digiwin.escloud.aiocdp.industry.service;

import com.digiwin.escloud.aiocdp.industry.model.SendMsgReqBody;
import com.digiwin.escloud.common.response.BaseResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date: 2025-05-06 14:18
 * @Description
 */
public interface IIndustryService {
    BaseResponse sendMsg(SendMsgReqBody msgReqBody);

    void setRedirect(String sourceId, String sourceUrl, String urlStorage, String sourceText, String sourceType, String userId,
                     String operateType, HttpServletResponse response) throws IOException;

    Long readFeedback(String sourceId, String sourceType);

    String getReadNum(String sourceId, String sourceType);

    void syncStaff();
}
