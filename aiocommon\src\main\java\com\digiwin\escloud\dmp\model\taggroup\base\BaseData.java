package com.digiwin.escloud.dmp.model.taggroup.base;

import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.dmp.model.basicmodel.Dimension;
import com.digiwin.escloud.dmp.model.taggroup.RuleGroup;
import com.digiwin.escloud.dmp.model.taggroup.TagWeightMapping;
import com.digiwin.escloud.dmp.model.taggroup.UpdateMode;
import com.digiwin.escloud.dmp.model.taggroup.ValueType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 11:17
 * @Description
 */
@Data
public class BaseData {
    private long id;
    private Long sid;
    private Long eid;
    private String appCode;
    private String code;
    private String name;
    @ApiModelProperty("维度id")
    private long dimensionId;
    private Dimension dimension;
    @ApiModelProperty("目录id")
    private long catalogueId;
    private String remark;
    @ApiModelProperty("标签或者分群值类型")
    private ValueType valueType;
    @ApiModelProperty("更新模式")
    private UpdateMode updateMode;
    @ApiModelProperty("间隔值")
    private int intervalValue;
    @ApiModelProperty("单位:年月日")
    private UnitType unit;
    @ApiModelProperty("cron表达式")
    private String cronString;
    @ApiModelProperty("具体时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date timingTime;
    @ApiModelProperty("数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dataUpdateTime;
    private Integer updateStatus;
    private long schedulerId;
    private List<RuleGroup> ruleGroups;

    /**
     * 标签无分层得权重数据
     */
    @ApiModelProperty("标签权重")
    private TagWeightMapping tlwm;
}
