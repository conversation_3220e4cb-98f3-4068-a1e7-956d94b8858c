package com.digiwin.escloud.aiouser.service;

import com.digiwin.escloud.aiouser.model.tenantNotice.*;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;
import java.util.Map;

/**
 * @Date 2021/6/21 11:46
 * @Created yanggld
 * @Description
 */
public interface ITenantNotifyService {
    /**
     * 列表查询
     * @param params
     * @return
     */
    List<TenantNotifyGroupListRespDTO> list(Map<String,Object> params);


    /**
     * 通过id查询通知群组详情
     * @param id
     * @return
     */
    TenantNotifyGroupRespDTO findByTenantNoticeGroupId(Long id);

    /**
     * 通过id查询通知群组详情
     * @param id
     * @return
     */
    TenantNotifyGroup findById(Long id);

    /**
     * 通过分组id查询通知通知人列表
     * @param noticeGroupId
     * @return
     */
    List<UserNotifyContact> findUserNoticeContactByNoticeGroupId(Long noticeGroupId);

    /**
     * 保存租户群组和通知人
     * @param dto
     */
    void save(TenantNotifyGroupRespDTO dto);

    /**
     * 删除租户群组和通知人
     * @param id
     */
    void delete(Long id);

    /**
     * 设置默认群组
     * @param id
     */
    void setDefault(Long id);

    List<UserNotifyContact> getUserNotifyContact(List<Long> tngIdList);

    /**
     * 依据客服代号获取默认通知用户
     * @param serviceCodeOrEid 客服代号或租户Sid
     * @param isDefault 是否是默认
     * @return 回覆对象
     */
    BaseResponse getUserNoticeContactByServiceCode(String serviceCodeOrEid, Boolean isDefault);

    int updateTenantNotifyGroup(TenantNotifyGroup tenantNotifyGroup);

    List<UserNotifyContact> getUserNoticeContactByMail(String eid, List<String> mailList);

    /**
     * 获取通知模块
     *
     * @return
     */
    List<NotifyModule> getNotifyModule();

    /**
     * 获取预警通知级别
     *
     * @param nmId
     * @return
     */
    List<NotifyReceiveLevel> getNotifyReceiveLevel(Long nmId);

    /**
     * 获取通知方式
     *
     * @param nmId
     * @return
     */
    List<NotifyWay> getNotifyWay(Long nmId);

    /**
     * 保存通知模块
     *
     * @return
     */
    ResponseBase saveNotifyModule(UserNotifyModuleSettingReq userNotifySetting);

    /**
     * 获取通知模块设定
     *
     * @param nmId
     * @return
     */
    ResponseBase getNotifyModuleSetting(Long nmId, String authUserId, Long eid);

    /**
     * 获取群组通知人
     *
     * @param eid
     * @param id
     * @return
     */
    ResponseBase getGroupNotifier(Long eid, Long id);

    /**
     * 修正设定数据
     *
     * @param eidList
     * @return
     */
    ResponseBase fixNotifySetting(List<Long> eidList);

    /**
     * 通过openId查看用户是否关注公众号
     *
     * @param userId
     * @return
     */
    ResponseBase getUserWechatSubscribe(String userId);

    /**
     * 清理user_notify_contact表中的重复数据
     * 保留userId相同的记录中id最大的一条，删除其他重复记录
     * 并更新tenant_notify_group_user_mapping表中对应的uncId
     *
     * @return 清理结果
     */
    ResponseBase cleanupDuplicateUserNotifyContact();
}
