package com.digiwin.escloud.aiochat.aichat.model;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

public class ChatMessageBuilderUtil {
    /**
     * 构建 ChatMessage 通用方法
     * @param headerInfo HeaderInfo 对象
     * @param questionStr 问题描述内容
     * @param agentId agentId
     * @return ChatMessage
     */
    public static ChatMessage buildChatMessage(HeaderInfo headerInfo, String questionStr, String agentId) {
        // 构建Question
        Question question = new Question();
        question.setHeaderInfo(JSONObject.toJSONString(headerInfo));
        question.setQuestion(questionStr);

        // 构建ChatMessage
        ChatMessage message = new ChatMessage();
        message.setQuestion(JSONObject.toJSONString(question));
        message.setSessionId("");
        message.setAgentId(agentId);
        message.setInputId(UUID.randomUUID().toString());
        message.setOutputId(UUID.randomUUID().toString());
        message.setIsEmptyQuestion(StringUtils.isEmpty(questionStr));
        return message;
    }
} 