package com.digiwin.escloud.aiochat.aichat.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiochat.aichat.ChatService;
import com.digiwin.escloud.aiochat.aichat.common.IndepthAICommon;
import com.digiwin.escloud.aiochat.aichat.model.ChatAssistant;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessage;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessageFeedback;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessageV2;
import com.digiwin.escloud.aiochat.aichat.dao.ChatAssistantMapper;
import com.digiwin.escloud.aiochat.common.BigDataUtil;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.exception.BizException;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.ConvertUtil;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.api.iam.req.user.IamAuthoredUser;
import com.digiwin.escloud.integration.service.IamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Slf4j
@Service
public class ChatServiceImpl extends IndepthAICommon implements ChatService , ParamCheckHelp {

    @Resource(name = "indepthAIWebClient")
    private WebClient webClient;

    @Resource
    private ChatAssistantMapper chatAssistantMapper;

    @Value("${esc.integration.appToken}")
    private String appToken;

    @Resource
    private IamService iamService;

    @Resource
    private BigDataUtil bigDataUtil;

    @Value("${digiwin.token.closed.beta.user.verifyuserid}")
    private String verifyUserId;
    @Value("${digiwin.token.closed.beta.tenant.id}")
    private String tenantId;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * c
     * @param message
     * @return
     */
    @Override
    public Flux<ServerSentEvent<Object>> invokeIndepthAIAgent(ChatMessage message) {

        try {
            Optional<BaseResponse> optResponse = checkParamIsEmpty(message.getAgentId(), "agentId");
            if (optResponse.isPresent()) {
                ResponseBase rb = new ResponseBase();
                BeanUtils.copyProperties(optResponse.get(), rb);
                throw new BizException(rb);
            }

            optResponse = checkParamIsEmpty(message.getInputId(), "inputId");
            if (optResponse.isPresent()) {
                ResponseBase rb = new ResponseBase();
                BeanUtils.copyProperties(optResponse.get(), rb);
                throw new BizException(rb);
            }

            optResponse = checkParamIsEmpty(message.getOutputId(), "outputId");
            if (optResponse.isPresent()) {
                ResponseBase rb = new ResponseBase();
                BeanUtils.copyProperties(optResponse.get(), rb);
                throw new BizException(rb);
            }

            message.setUserId(RequestUtil.getHeaderUserId());
            message.setUserSid(RequestUtil.getHeaderUserSid());
            message.setSid(RequestUtil.getHeaderSid());
            ChatAssistant chatAssistant = getIndepthAICode(message.getAgentId());
            message.setAiAgentId(chatAssistant.getAiAgentId());
            message.setAgentCode(chatAssistant.getCode());
            message.setEid(RequestUtil.getHeaderEid());

            buildChatMessageV2(message,chatAssistant);
            log.info("[invokeIndepthAIAgent] req:{}", JSONObject.toJSONString(message));
            Flux<ServerSentEvent<Object>> fluxRes = webClient.post()
                    .uri("/" + chatAssistant.getCode())
                    .header("token", getAuthedToken())
                    .header("Digi-Middleware-Auth-App", getAppToken())
                    .header("Accept-Language", getLanguage())
                    .bodyValue(message)
                    .retrieve()
                    // 使用 ServerSentEvent 类型解析
                    .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<Object>>() {
                    })
    //                .doOnNext(event ->
    //                        log.info("[SSE Event] id:{}, event:{}, data:{}",
    //                                event.id(), event.event(), event.data())
    //                );
                    ;

            return this.packageFlowProcess(message, fluxRes);
        } catch (Exception e) {
            log.error("[invokeIndepthAIAgent] error:{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public ResponseBase saveChatMessageFeedback(ChatMessageFeedback feedback) {
        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setTable("ChatMessageFeedback");
        starRocksEntity.setDatabase("servicecloud");

        // 使用反射获取ChatMessageV2的所有属性名
        List<String> fieldNames = getFieldNames(ChatMessageFeedback.class);
        starRocksEntity.setFieldNames(fieldNames.toArray(new String[0]));
        String nowTime = DateUtil.getNowFormatString(DATE_TIME_FORMATTER);
        feedback.setFeedbackId(SnowFlake.getInstance().newId());
        feedback.setCreateTime(nowTime);
        feedback.setUpdateTime(nowTime);

        List<LinkedHashMap<String, Object>> rows = new ArrayList<>();
        rows.add(convertToMap(feedback));
        starRocksEntity.setRows(rows);

        try {
            Map<String, Object> map = bigDataUtil.srStreamLoadThrowsException(starRocksEntity);
            BaseResponse baseResponse = ConvertUtil.convertToObject(map, BaseResponse.class);
            if (!baseResponse.checkIsSuccess()) {
                log.error("[saveChatMessageFeedback] error : {}", JSONObject.toJSONString(baseResponse));
                return ResponseBase.error(baseResponse.getCode(), baseResponse.getErrMsg());
            }
        } catch (Exception e) {
            log.error("[saveChatMessageFeedback] error : {}", e.getMessage(),e);
        }
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase updateChatAssistantByAiAgentId(ChatAssistant chatAssistant) {
        try {
            if (chatAssistant == null || chatAssistant.getAiAgentId() == null) {
                return ResponseBase.error("INVALID_PARAM", "aiAgentId不能为空");
            }

            // 设置更新时间
            chatAssistant.setUpdateTime(java.time.LocalDateTime.now());

            int result = chatAssistantMapper.updateChatAssistantByAiAgentId(chatAssistant);
            if (result > 0) {
                return ResponseBase.ok("更新成功");
            } else {
                return ResponseBase.error("UPDATE_FAILED", "更新失败，未找到对应的记录");
            }
        } catch (Exception e) {
            log.error("[updateChatAssistantByAiAgentId] error : {}", e.getMessage(), e);
            return ResponseBase.error("INTERNAL_ERROR", "更新失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseBase insertChatAssistant(ChatAssistant chatAssistant) {
        try {
            if (chatAssistant == null) {
                return ResponseBase.error("INVALID_PARAM", "chatAssistant不能为空");
            }

            // 检查必填字段
            if (chatAssistant.getAiAgentId() == null || chatAssistant.getAiAgentId().trim().isEmpty()) {
                return ResponseBase.error("INVALID_PARAM", "aiAgentId不能为空");
            }

            // 检查是否已存在
            ChatAssistant existing = chatAssistantMapper.selectChatAssistantByAgentId(chatAssistant.getAiAgentId());
            if (existing != null) {
                return ResponseBase.error("DUPLICATE_RECORD", "该aiAgentId已存在");
            }

            // 设置ID和时间
            if (chatAssistant.getId() == null || chatAssistant.getId().trim().isEmpty()) {
                chatAssistant.setId(java.util.UUID.randomUUID().toString());
            }
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            chatAssistant.setCreateTime(now);
            chatAssistant.setUpdateTime(now);

            int result = chatAssistantMapper.insertChatAssistant(chatAssistant);
            if (result > 0) {
                return ResponseBase.ok(chatAssistant);
            } else {
                return ResponseBase.error("INSERT_FAILED", "新增失败");
            }
        } catch (Exception e) {
            log.error("[insertChatAssistant] error : {}", e.getMessage(), e);
            return ResponseBase.error("INTERNAL_ERROR", "新增失败：" + e.getMessage());
        }
    }

    private void buildChatMessageV2(ChatMessage message, ChatAssistant chatAssistant) {
        ChatMessageV2 chatMessageV2 = new ChatMessageV2();
        chatMessageV2.setMessageId(message.getInputId());
        chatMessageV2.setSid(message.getSid());
        chatMessageV2.setEid(message.getEid());
        chatMessageV2.setUserSid(message.getUserSid());
        chatMessageV2.setUserId(message.getUserId());
        chatMessageV2.setEvent("question");
        chatMessageV2.setEventAction("question");
        chatMessageV2.setStatus("done");
        chatMessageV2.setMessage(message.getQuestion());
        chatMessageV2.setQuestion(JSONObject.parseObject(message.getQuestion()).getString("question"));
        chatMessageV2.setQuestionId(UUID.randomUUID().toString());
        chatMessageV2.setSender(message.getUserId());
        chatMessageV2.setReceiver(chatAssistant.getCode());
        chatMessageV2.setAgentCode(chatAssistant.getCode());
        chatMessageV2.setAiAgentId(chatAssistant.getAiAgentId());
        message.setChatMessageV2(chatMessageV2);
    }

    private ChatAssistant getIndepthAICode(String agentId){
        // 鲸宝会话使用 INTELLIGENT_Q_A  //信息汇总 AGGREGATION_INFORMATION
        ChatAssistant chatAssistant = chatAssistantMapper.selectChatAssistantByAgentId(agentId);
        if (Objects.isNull(chatAssistant)) {
            throw new RuntimeException();
        }
        return chatAssistant;
    }

    private String getAuthedToken() {
        String betaToken = "closed:beta:ai:agent:token";
        // 模拟丽香token调用api
        String tokenVal = stringRedisTemplate.opsForValue().get(betaToken);
        if (StringUtils.isEmpty(tokenVal)) {
            tokenVal = iamService.grantAccessToken(verifyUserId, tenantId);
            IamAuthoredUser iamAuthoredUser = iamService.analyzeToken(tokenVal);
            if (iamAuthoredUser == null) {
                log.error("getAuthedToken null  verifyUserId:{} , tenantId:{}",verifyUserId, tenantId);
                throw new RuntimeException("request contains invalid token");
            }
            stringRedisTemplate.opsForValue().set(betaToken, tokenVal, iamAuthoredUser.getTokenExpiresIn(), TimeUnit.MILLISECONDS);
        }
        log.info("getAuthedToken tokenVal:{}", tokenVal);
        return tokenVal;
    }

    private String getAppToken(){

        return appToken;
    }

    private String getLanguage(){
        List<String> array = new ArrayList<>();
        array.add("zh-CN");
        array.add("zh-TW");
        array.add("zh-EN");
        String acceptLanguage = RequestUtil.getAcceptLanguage();
        if (StringUtils.isEmpty(acceptLanguage) || !array.contains(acceptLanguage)) {
            return "zh-CN";
        }
        return acceptLanguage;
    }

    @Override
    protected void callApiError(Throwable error, String askMessage) {

    }

    @Override
    protected void saveMsg(Object procDomainObject, String fullFlowMsg, String msgSourceList, Throwable error, ChatMessage chatMessage) {

    }

    @Override
    protected void saveMsg(Object procDomainObject, Object procDomainDoneObject, String fullFlowMsg, String msgSourceList, ChatMessage chatMessage) {
        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setTable("ChatMessageRecord");
        starRocksEntity.setDatabase("servicecloud");
        
        // 使用反射获取ChatMessageV2的所有属性名
        List<String> fieldNames = getFieldNames(ChatMessageV2.class);
        starRocksEntity.setFieldNames(fieldNames.toArray(new String[0]));
        String nowTime = DateUtil.getNowFormatString(DATE_TIME_FORMATTER);
        // 将对象转换为Map并添加到rows中
        List<LinkedHashMap<String, Object>> rows = new ArrayList<>();
        ChatMessageV2 startChatMessageV2 = (ChatMessageV2) procDomainObject;
        if (startChatMessageV2 != null) {
            startChatMessageV2.setCreateTime(nowTime);
            startChatMessageV2.setUpdateTime(nowTime);
            rows.add(convertToMap(startChatMessageV2));
        }
        ChatMessageV2 doneChatMessageV2 = (ChatMessageV2) procDomainDoneObject;
        if (doneChatMessageV2 != null) {
            ChatMessageV2 question = chatMessage.getChatMessageV2();
            question.setSessionId(doneChatMessageV2.getSessionId());
            question.setCreateTime(nowTime);
            question.setUpdateTime(nowTime);
            rows.add(convertToMap(question));
            // 节点结束 回答问题 特殊设置id
            doneChatMessageV2.setMessageId(chatMessage.getOutputId());
            doneChatMessageV2.setMessageSourceList(msgSourceList);
            JSONObject jsonObject = JSONObject.parseObject(doneChatMessageV2.getMessage());
            jsonObject.put("message", fullFlowMsg);
            doneChatMessageV2.setMessage(JSONObject.toJSONString(jsonObject));
            doneChatMessageV2.setSender(doneChatMessageV2.getAgentCode());
            doneChatMessageV2.setReceiver(doneChatMessageV2.getUserId());
            doneChatMessageV2.setQuestionId(question.getQuestionId());
            doneChatMessageV2.setQuestion(question.getMessage());
            doneChatMessageV2.setCreateTime(nowTime);
            doneChatMessageV2.setUpdateTime(nowTime);
            rows.add(convertToMap(doneChatMessageV2));
        }

        if (log.isDebugEnabled()) {
            log.info("[saveMsg] procDomainObject:{}", procDomainObject);
            log.info("[saveMsg] procDomainDoneObject:{}", procDomainDoneObject);
            log.info("[saveMsg] msgSourceList:{}", msgSourceList);
        }


        starRocksEntity.setRows(rows);
        if (rows.isEmpty()) {
            log.info("[saveMsg] rows:{}", rows);
            return;
        }

        try {
            Map<String, Object> map = bigDataUtil.srStreamLoadThrowsException(starRocksEntity);
            BaseResponse baseResponse = ConvertUtil.convertToObject(map, BaseResponse.class);
            if (!baseResponse.checkIsSuccess()) {
               log.error("[saveMsg] error : {}", JSONObject.toJSONString(baseResponse));
            }
        } catch (Exception e) {
            log.error("[saveMsg] error : {}", e.getMessage(),e);
        }
    }

    private List<String> getFieldNames(Class<?> clazz) {
        List<String> fieldNames = new ArrayList<>();
        java.lang.reflect.Field[] fields = clazz.getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            fieldNames.add(field.getName());
        }
        return fieldNames;
    }

    private <T> LinkedHashMap<String, Object> convertToMap(T object) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        java.lang.reflect.Field[] fields = object.getClass().getDeclaredFields();
        for (java.lang.reflect.Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(object);
                if (value == null) {
                    map.put(field.getName(), "");
                } else {
                    map.put(field.getName(), value);
                }

            } catch (IllegalAccessException e) {
                log.error("Failed to get field value: {}", field.getName(), e);
            }
        }
        return map;
    }

    @Override
    protected Object getProcessDomainObject(ChatMessage chatMessage, Object recMsg, String eventId) {
        Map<String, Object> flowRow = (Map<String, Object>) recMsg;

//        String returnSampleJson = "{\n" +
//                "    \"action\": \"answer\",\n" +
//                "    \"blockId\": \"4e8fd224-1c77-402a-8501-33fd6603e57f\",\n" +
//                "    \"message\": \"昨天上午 10:30 提的案件:111199 的问题处理好了吗？什么时候给我打电话?\\n{<br>  \\\"eid\\\": 99990000,<br>  \\\"deviceId\\\": 8759487583,<br>  \\\"userId\\\": \\\"asam_user_id\\\",<br>  \\\"case_no\\\": [\\\"111199\\\"],<br>  \\\"query_start_date\\\": \\\"2025-04-10 10:30:00\\\",<br>  \\\"query_end_date\\\": \\\"2025-04-10 10:30:00\\\"<br>}\",\n" +
//                "    \"messageSource\": \"text_reply\",\n" +
//                "    \"nodeId\": \"7b26b6c3-6bde-4b93-b6ba-a4d72c8b5cc5\",\n" +
//                "    \"sessionId\": \"91539a51-e6cb-4886-b2be-070fe3eaffc1\",\n" +
//                "    \"status\": \"done\"\n" +
//                "}";

        String action = this.getReturnFlowValue("action", flowRow);
        String blockId = this.getReturnFlowValue("blockId", flowRow);
//        String message = this.getReturnFlowValue("message", flowRow);
        String messageSource = this.getReturnFlowValue("messageSource", flowRow);
        String nodeId = this.getReturnFlowValue("nodeId", flowRow);
        String sessionId = this.getReturnFlowValue("sessionId", flowRow);
        String status = this.getReturnFlowValue("status", flowRow);

        ChatMessageV2 chatMsgRow = new ChatMessageV2();

        // basic info
        chatMsgRow.setMessageId(eventId);
        chatMsgRow.setSid(chatMessage.getSid());
        chatMsgRow.setUserSid(chatMessage.getUserSid());
        chatMsgRow.setUserId(chatMessage.getUserId());
        chatMsgRow.setEid(chatMessage.getEid());

        chatMsgRow.setEvent(action);
        chatMsgRow.setEventAction(action);
        chatMsgRow.setMessage(JSONObject.toJSONString(flowRow));
        chatMsgRow.setMessageSource(messageSource);
        // user ask
        chatMsgRow.setQuestion(chatMessage.getQuestion());
        chatMsgRow.setAgentCode(chatMessage.getAgentCode());
        chatMsgRow.setAiAgentId(chatMessage.getAiAgentId());

        // from ai reply
        chatMsgRow.setSessionId(sessionId);
        chatMsgRow.setBlockId(blockId);
        chatMsgRow.setNodeId(nodeId);
        chatMsgRow.setStatus(status);

        // system info
        chatMsgRow.setSender("");
        chatMsgRow.setReceiver("");


        return chatMsgRow;
    }
}
