package com.digiwin.escloud.aioitms.util;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.dmp.model.taggroup.Tag;
import org.springframework.core.ParameterizedTypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;


@Slf4j
@Component
public class RestDmpUtil {
    @Value("${aiops.base.url:http://172.16.1.152:30010/}")
    private String aioBaseUrl;
    @Value("${dcdp.dmp.url:https://es-dcp.digiwincloud.com.cn/dcdpgateway/dcdpdmp}")
    private String dcdpDmpUrl;
    @Resource(name = "restDcdp")
    private RestTemplate restTemplate;



    public ResponseBase getTagData(Collection<Long> tagIdList) {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity request = new HttpEntity(tagIdList, headers);
        String url = dcdpDmpUrl + "/api/tag/getTagData";
        ResponseEntity<ResponseBase> exchange = restTemplate.exchange(url, HttpMethod.POST, request, ResponseBase.class);
        return exchange.getBody();
    }



    public ResponseBase getTagCount(List<Long> tagIdList,String eid,String appCode) {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.set("Eid", eid);
        headers.set("AppCode", appCode);
        HttpEntity request = new HttpEntity(tagIdList, headers);
        String url = dcdpDmpUrl + "/api/tag/getTagCount";
        ResponseEntity<ResponseBase> exchange = restTemplate.exchange(url, HttpMethod.POST, request, ResponseBase.class);
        return exchange.getBody();
    }

    public ResponseBase getTagTable(List<Long> tagIdList,String eid,String appCode) {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.set("Eid",eid);
        headers.set("AppCode",appCode);
        HttpEntity request = new HttpEntity(tagIdList, headers);
        String url = dcdpDmpUrl + "/api/tag/getTagTable";
        ResponseEntity<ResponseBase> exchange = restTemplate.exchange(url, HttpMethod.POST, request, ResponseBase.class);
        return exchange.getBody();
    }

    public ResponseEntity<JSONObject> getPortraitImportantTag(Long objId) {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity request = new HttpEntity(headers);

        String url = dcdpDmpUrl + "/portrait/important/tag/list?objId=" + objId;
        return restTemplate.exchange(url, HttpMethod.GET, request, JSONObject.class);
    }

    public ResponseBase<List<Tag>> getTagDetailList(List<Long> tagIdList) {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity request = new HttpEntity(tagIdList, headers);
        String url = dcdpDmpUrl + "/api/tag/getTagDetailList";
        ResponseEntity<ResponseBase<List<Tag>>> exchange = restTemplate.exchange(
            url,
            HttpMethod.POST,
            request,
            new ParameterizedTypeReference<ResponseBase<List<Tag>>>() {}
        );
        return exchange.getBody();
    }

}
