package com.digiwin.escloud.dmp.model.condition;

import com.digiwin.escloud.common.model.LogicType;
import com.digiwin.escloud.dmp.model.taggroup.FieldType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2022-04-21 17:37
 * @Description
 */
@Data
public class EventConditionDetail {
    private long id;
    @ApiModelProperty("事件筛选备注名")
    private String name;
    @ApiModelProperty("事件条件id")
    private long decId;
    @ApiModelProperty("字段编号")
    private String fieldCode;
    @ApiModelProperty("操作符")
    private String operator;
    @ApiModelProperty("值")
    private String operatorValue;
    @ApiModelProperty("值类型")
    private String operatorType;
    @ApiModelProperty("左值")
    private String leftOperatorValue;
    @ApiModelProperty("左值类型")
    private String leftOperatorType;
    @ApiModelProperty("右值")
    private String rightOperatorValue;
    @ApiModelProperty("右值类型")
    private String rightOperatorType;
    @ApiModelProperty("字段类型")
    private FieldType fieldType;
    @ApiModelProperty("逻辑符")
    private LogicType logic;
    @ApiModelProperty("序号")
    private int orderNum;
    @ApiModelProperty("左括号")
    private String leftBracket;
    @ApiModelProperty("右括号")
    private String rightBracket;
}
