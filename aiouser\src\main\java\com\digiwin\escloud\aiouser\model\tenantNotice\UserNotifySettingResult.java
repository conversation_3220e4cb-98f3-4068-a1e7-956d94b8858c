package com.digiwin.escloud.aiouser.model.tenantNotice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserNotifySettingResult {

    private Long id;
    private Long userSid;
    private String userId;
    private Long nmId;
    private String nwIds;    // 用字符串处理逗号分隔的ID
    private String nrlIds;   // 用字符串处理逗号分隔的ID
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
