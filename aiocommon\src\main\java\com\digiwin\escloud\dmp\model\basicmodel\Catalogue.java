package com.digiwin.escloud.dmp.model.basicmodel;

import com.digiwin.escloud.common.model.BaseType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date: 2022-04-21 17:12
 * @Description
 */
@Data
@NoArgsConstructor
// (tableName="dmp_catalogue",groups = ModifyGroup.class)
public class Catalogue extends BaseType {
    private Long sid;
    private Long eid;
    private String appCode;
    private CatalogueType catalogueType;
    private long parentId;
    private long dimensionId;

    public Catalogue(long id, String code, String name, String appCode, CatalogueType catalogueType,
                     long parentId, long dimensionId) {
        this.setId(id);
        this.setCode(code);
        this.setName(name);
        this.setAppCode(appCode);
        this.setCatalogueType(catalogueType);
        this.setParentId(parentId);
        this.setDimensionId(dimensionId);
    }
}
