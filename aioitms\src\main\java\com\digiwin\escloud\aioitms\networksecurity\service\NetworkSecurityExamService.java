package com.digiwin.escloud.aioitms.networksecurity.service;

import com.digiwin.escloud.aioitms.bigdata.NetworkSecurityDataParams;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsReportRecord;
import com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamAssetRequest;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamRequest;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;

public interface NetworkSecurityExamService {
    /**
     * 根据体检基础数据 查询体检项目
     *
     * @return
     */
    ResponseBase getNetworkSecurityExam(NetworkSecurityExamRequest request);

    BaseResponse saveNetworkSecurityExamRecord(AiopsExamRecord record);

    ResponseBase saveOrUpdateProjectType(NetworkSecurityExaminationProjectType projectType);

    ResponseBase<List<NetworkSecurityExaminationProjectType>> getProjectType(String categoryCode, String categoryName, String parentCode, Boolean filterNullModel, Long aerId, Long eid);

    ResponseBase saveNetworkSecurityAsset(List<AiopsExamItemInstanceScore> aeiisList,Long recordId);

    BaseResponse getNetworkSecurityExamAsset(NetworkSecurityExamAssetRequest request);

    BaseResponse deleteNetworkSecurityExamAsset(String aiopsItemId, Long recordId);

    BaseResponse saveNetworkSecurityExamReport(AiopsExamRecordsReportRecord reportRecord);

    BaseResponse getNetworkSecurityExamReport(Long reportRecordId, int scale);

    ResponseBase<Long> getNetworkSecurityAe(String reportType);

    ResponseBase getEnableAeim(String reportType);

    BaseResponse getAiopsItem(String modelCode, Long eid);

    BaseResponse getProjectInstance(String aiopsItem, Long eid);

    BaseResponse saveNetworkSecurityData(NetworkSecurityDataParams networkSecurityDataParams);

    BaseResponse deleteNetworkSecurityDataById(NetworkSecurityDataParams networkSecurityDataParams);

    ResponseBase<Long> getNetworkSecurityAe(AiopsExamRecord record);

    ResponseBase getNetworkSecurity(Long eid, Long aerId);

    /**
     * 保存网安简式报告，简式报告得分数也会保存到es中（因为要修改报告分数得健康结果，不能影响原有得健康结果），完整报告得分数是在mysql中查询得
     *
     * @param reportRecord
     * @param scale
     * @return
     */
    BaseResponse saveNetworkSecurityExamSimpleReport(AiopsExamRecordsReportRecord reportRecord,int scale);

    /**
     * 查询网安简式报告
     * @param id 报告ID
     * @return 简式报告
     */
    BaseResponse getNetworkSecurityExamSimpleReport(String id);

    /**
     * 更新网安简式报告
     * @param id 报告ID
     * @param fieldPath 字段路径
     * @param value 新值
     * @return 结果
     */
    BaseResponse updateNetworkSecurityExamSimpleReport(String id, String fieldPath, Object value);

    BaseResponse getExamItemInstanceScoreTotal(Long aerId);

    BaseResponse selectExamRecord(Long eid, int scale);

    BaseResponse getDbBackupSuccessDetail (String eid, String timeFrom, String timeTo);

}
