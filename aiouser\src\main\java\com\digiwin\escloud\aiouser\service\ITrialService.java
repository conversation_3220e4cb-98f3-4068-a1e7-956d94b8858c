package com.digiwin.escloud.aiouser.service;

import com.digiwin.escloud.aiouser.model.trial.*;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-01-06 18:31
 * @Description
 */
public interface ITrialService {

    boolean saveTrialApply(TrialApply trialApply);

    List<TrialApply> getTrialApplies(TrialApplyGetParam trialApplyGetParam);

    int getTrialAppliesCount(TrialApplyGetParam trialApplyGetParam);

    /**
     * 开通试用
     * @param trialOpen 开通试用对象
     * @return 回覆对象
     */
    BaseResponse openTrial(TrialOpen trialOpen);

    boolean saveInstallInfo(TrialOpen trialOpen);

    TrialOpen getTrialOpenDetail(long applyId);

    /**
     * 获取空试用模组合约列表
     * @return 回覆对象
     */
    BaseResponse getTrialModuleContractEmptyList();

    boolean installSuccess(long applyId, Date installTime);

    boolean installFail(long applyId, String failReason);

    List<TrialApplyRejectMap> getTrialApplyRejectReasons(long applyId);

    boolean saveRejectTrialApply(List<TrialApplyRejectMap> trialApplyRejectMaps);

    List<TrialApplyRejectReason> getRejectReasons();

    void sendModuleContractExpireMail(Long specifyCheckEid);

    List<TrialApplySource> getTrialApplySources(Integer sourceType, int page, int size);

    int getTrialApplySourcesCount(Integer sourceType);

    List<TrialApplySource> getTrialApplyActiveList(String themeModelCode);

    ResponseBase saveTrialApplySource(TrialApplySource trialApplySource);
}
