<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocdp.industry.dao.IIndustryDao">

    <insert id="saveCdpLog" parameterType="com.digiwin.escloud.aiocdp.industry.model.CdpLog">
        INSERT into `${industryDBName}`.cdp_log (`id`, `eid`, `userId`, `sourceId`, `sourceText`, `sourceType`, `sourceUrl`,
                `urlStorage`,`urlMd5`,`operateType`,`operateContent`, `operateResult`, `startTime`, `endTime`)
        <foreach collection="cdpLogs" item="item" open="VALUES (" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.userId}, #{item.sourceId}, #{item.sourceText}, #{item.sourceType},
            #{item.sourceUrl}, #{item.urlStorage}, #{item.urlMd5}, #{item.operateType}, #{item.operateContent}, #{item.operateResult},
            #{item.startTime}, #{item.endTime}
        </foreach>
    </insert>

    <insert id="saveStaff" parameterType="com.digiwin.escloud.aiocdp.industry.model.Staff">
        INSERT into `${industryDBName}`.user_staff (`workno`, `itcode`, `fullname`, `email`, `dptId`, `dptName`, `dptNameCN`, `dptNameTW`,
               `upperDptId`, `upperDptName`, `upperDptNameCN`, `upperDptNameTW`, `supervisor`, `workStatus`)
        <foreach collection="staffList" item="item" open="VALUES (" separator="), (" close=")">
            #{item.workno}, #{item.itcode}, #{item.fullname}, #{item.email}, #{item.dptId}, #{item.dptName},
            #{item.dptNameCN}, #{item.dptNameTW}, #{item.upperDptId}, #{item.upperDptName}, #{item.upperDptNameCN}, #{item.upperDptNameTW},
            #{item.supervisor}, #{item.workStatus}
        </foreach>
        ON DUPLICATE KEY UPDATE
            `itcode` = VALUES(`itcode`), `fullname` = VALUES(`fullname`),`email` = VALUES(`email`),
            `dptId` = VALUES(`dptId`), `dptName` = VALUES(`dptName`), `dptNameCN` = VALUES(`dptNameCN`), `dptNameTW` = VALUES(`dptNameTW`),
            `upperDptId` = VALUES(`upperDptId`), `upperDptName` = VALUES(`upperDptName`), `upperDptNameCN` = VALUES(`upperDptNameCN`),
            `upperDptNameTW` = VALUES(`upperDptNameTW`), `supervisor` = VALUES(`supervisor`), `workStatus` = VALUES(`workStatus`)
    </insert>
</mapper>