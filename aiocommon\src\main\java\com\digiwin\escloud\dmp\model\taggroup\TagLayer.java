package com.digiwin.escloud.dmp.model.taggroup;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 10:20
 * @Description
 */
@Data
@NoArgsConstructor
public class TagLayer {
    private long id;
    @ApiModelProperty("分组id")
    private long drgId;
    @ApiModelProperty("分层名称")
    private String name;
    @ApiModelProperty("左值")
    private String leftOperatorValue;
    @ApiModelProperty("右值")
    private String rightOperatorValue;
    @ApiModelProperty("值类型")
    private FieldType fieldType;
    @ApiModelProperty("右操作符")
    private String leftOperator;
    @ApiModelProperty("左操作符")
    private String rightOperator;
    @ApiModelProperty("序号")
    private int orderNum;

    public TagLayer(String name) {
        this.name = name;
    }
    @ApiModelProperty("分层权重")
    private TagWeightMapping tlwm;
}
