<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiochat.aichat.dao.ChatAssistantMapper">
    <select id="selectChatAssistantByAgentId" resultType="com.digiwin.escloud.aiochat.aichat.model.ChatAssistant">
        select *
        from chat_assistant
        where aiAgentId = #{aiAgentId}
    </select>

    <update id="updateChatAssistantByAiAgentId" parameterType="com.digiwin.escloud.aiochat.aichat.model.ChatAssistant">
        update chat_assistant
        set code = #{code},
            name = #{name},
            appSkillId = #{appSkillId}
        where aiAgentId = #{aiAgentId}
    </update>

    <insert id="insertChatAssistant" parameterType="com.digiwin.escloud.aiochat.aichat.model.ChatAssistant">
        insert into chat_assistant (id, aiAgentId, code, name, appSkillId)
        values (#{id}, #{aiAgentId}, #{code}, #{name}, #{appSkillId})
    </insert>
</mapper>
