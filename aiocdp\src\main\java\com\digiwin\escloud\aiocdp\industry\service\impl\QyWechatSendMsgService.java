package com.digiwin.escloud.aiocdp.industry.service.impl;

import com.digiwin.escloud.aiocdp.constant.UcdpConst;
import com.digiwin.escloud.aiocdp.industry.annotation.SendTypeCode;
import com.digiwin.escloud.aiocdp.industry.model.SendMessageConfigParam;
import com.digiwin.escloud.aiocdp.industry.model.SendMessageDto;
import com.digiwin.escloud.aiocdp.industry.model.SendMsgReqBody;
import com.digiwin.escloud.aiocdp.industry.model.SendPicMessageDto;
import com.digiwin.escloud.aiocdp.industry.service.ISendMsgItem;
import com.digiwin.escloud.aiocdp.utils.UcdpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 2025-05-06 20:09
 * @Description
 */
@RefreshScope
@SendTypeCode("QY_WECHAT")
@Slf4j
@Service
public class QyWechatSendMsgService implements ISendMsgItem {

    @Resource
    private UcdpUtil ucdpUtil;

    @Override
    public Object buildMsg(SendMsgReqBody sendMsgReqBody) {
        SendMessageDto sendMessageDto = new SendMessageDto();
        List<String> allUserIds = sendMsgReqBody.getAllUserIds();
        if (CollectionUtils.isEmpty(allUserIds)) {
            return null;
        }
        //图文消息类型
        List<SendPicMessageDto.ArticleDTO> articles = new ArrayList<>();
        if (CollectionUtils.isEmpty(sendMsgReqBody.getMsgDtos())) {
            return null;
        }
        for (SendMsgReqBody.MsgDto msgDto : sendMsgReqBody.getMsgDtos()) {
            SendPicMessageDto.ArticleDTO articleDTO = new SendPicMessageDto.ArticleDTO(msgDto.getTitle(), msgDto.getDescription(),
                    msgDto.getLinkUrl(), msgDto.getPicUrl());
            articles.add(articleDTO);
        }
        SendPicMessageDto.New aNew = new SendPicMessageDto.New();
        aNew.setArticles(articles);
        sendMessageDto.setNews(aNew);
        sendMessageDto.setMsgtype("news");
        sendMessageDto.setEnableIdTrans(0);
        sendMessageDto.setEnableDuplicateCheck(0);
        sendMessageDto.setDuplicateCheckInterval(1800);
        return sendMessageDto;
    }

    @Override
    public void sendMsg(SendMsgReqBody sendMsgReqBody) {
        Object sendMessageDto = buildMsg(sendMsgReqBody);
        if (ObjectUtils.isEmpty(sendMessageDto)) {
            return;
        }
        SendMessageDto msgDto = (SendMessageDto) sendMessageDto;
        SendPicMessageDto.New picNews = msgDto.getNews();
        if (ObjectUtils.isEmpty(picNews)) {
            return;
        }
        if (CollectionUtils.isEmpty(picNews.getArticles())) {
            return;
        }
        List<String> urls = picNews.getArticles().stream().map(SendPicMessageDto.ArticleDTO::getUrl).collect(Collectors.toList());

        Integer authFlag = sendMsgReqBody.getAuthFlag();
        String authParam = sendMsgReqBody.getAuthParam();
        List<String> toUserEmail = sendMsgReqBody.getAllUserIds();
        for (String userEmail : toUserEmail) {
            SendMessageDto finalMsgDto = new SendMessageDto();
            BeanUtils.copyProperties(msgDto, finalMsgDto);
            SendPicMessageDto.New news = finalMsgDto.getNews();
            if (!CollectionUtils.isEmpty(urls)) {
                for (SendPicMessageDto.ArticleDTO article : news.getArticles()) {
                    article.setUrl(urls.get(0));
                    String url = article.getUrl();
                    if (StringUtils.isEmpty(url)) {
                        continue;
                    }
                    try {
                        String encodeMail = URLEncoder.encode(userEmail, "UTF-8");
                        url = url.replace(UcdpConst.EMAIL_PARAM, encodeMail);
                    } catch (UnsupportedEncodingException e) {
                        log.info("sendMsg URLEncoder.encode error", e);
                        continue;
                    }
                    article.setUrl(url);
                }
            }
            finalMsgDto.setToUserEmail(Stream.of(userEmail).collect(Collectors.toList()));
            SendMessageConfigParam sendMessageConfigParam = new SendMessageConfigParam();
            sendMessageConfigParam.setAuthParam(authParam);
            sendMessageConfigParam.setAuthFlag(authFlag);
            sendMessageConfigParam.setDto(finalMsgDto);
            try {
                ucdpUtil.sendQyWechatMsgV2(sendMessageConfigParam);
                Thread.sleep(500);
            } catch (Exception e) {
                log.error("sendQyWechatMsgV2 error", e);
            }
        }
    }
}
