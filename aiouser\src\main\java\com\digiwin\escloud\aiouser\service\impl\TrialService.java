package com.digiwin.escloud.aiouser.service.impl;

import com.digiwin.escloud.aioitms.model.collectapp.ProductAppCodeMapping;
import com.digiwin.escloud.aiouser.dao.ITenantDao;
import com.digiwin.escloud.aiouser.dao.ITrialDao;
import com.digiwin.escloud.aiouser.model.module.ModuleContractDetailBase;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModule;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModuleClass;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModuleItem;
import com.digiwin.escloud.aiouser.model.tenant.*;
import com.digiwin.escloud.aiouser.model.trial.*;
import com.digiwin.escloud.aiouser.service.ISupplierAiopsService;
import com.digiwin.escloud.aiouser.service.ITenantService;
import com.digiwin.escloud.aiouser.service.ITrialService;
import com.digiwin.escloud.aiouser.service.mail.MailFactoryService;
import com.digiwin.escloud.aiouser.util.AcpUtil;
import com.digiwin.escloud.aiouser.util.CommonUtils;
import com.digiwin.escloud.aiouser.util.MailUtils;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.TenantTpParams;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.integration.service.IamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2022-01-06 18:33
 * @Description
 */
@Service
@Slf4j
public class TrialService implements ITrialService, ParamCheckHelp {
    @Value("${service.area}")
    private String serviceArea;
    @Value("${digiwin.escloud.dbname:escloud-db}")
    private String escloudDBName;
    @Autowired
    private ITrialDao trialDao;
    @Autowired
    private ITenantDao tenantDao;
    @Autowired
    private ITenantService tenantService;
    @Autowired
    private ISupplierAiopsService supplierAiopsService;
    @Value("${remainDays:9,2,0,-1}")
    private List<Integer> remainDays;
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private String defaultSid;
    @Value("${check.expiration.moduleIds:}")
    private List<Integer> checkExpirationModuleIds;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Resource
    private MailFactoryService mailFactoryService;
    @Resource
    private CommonUtils commonUtils;
    @Resource
    private AcpUtil acpUtil;
    @Resource
    private MailUtils mailUtil;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Value("${digiwin.token.user.verifyuserid}")
    private String verifyUserId;
    @Value("${digiwin.token.tenant.id}")
    private String verifytenantId;
    @Autowired
    protected IamService iamService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTrialApply(TrialApply trialApply) {
        Long sid = RequestUtil.getEsHeaderSid(defaultSid);
        trialApply.setSid(sid);
        if (trialApply.getId() == 0) {
            trialApply.setId(SnowFlake.getInstance().newId());
            trialApply.setApplyStatus(ApplyStatus.APPLYING.getIndex());
            trialApply.setApplyTime(new Date());
            trialApply.setInstallStatus(InstallStatus.UNINSTALL.getIndex());
        } else {
            trialDao.deleteTrialApplyModuleMap(trialApply.getId());
        }
        if (trialApply.getApplySourceId() == 0) {
            trialApply.setApplySourceId(trialDao.getTrialApplySourceId(trialApply.getApplySourceCode()));
        }
        if (!CollectionUtils.isEmpty(trialApply.getTrialApplyModuleMaps())) {
            trialApply.getTrialApplyModuleMaps().stream().forEach(o -> {
                o.setId(SnowFlake.getInstance().newId());
                o.setSid(sid);
                o.setApplyId(trialApply.getId());
            });
            trialDao.saveTrialApplyModuleMap(trialApply.getTrialApplyModuleMaps());
        }
        boolean res = trialDao.saveTrialApply(trialApply) > 0;
        if (res && !TrialApplyDefaultSource.SERVICE_INVITATION.toString().equals(trialApply.getApplySourceCode())) {
            commonUtils.asyncRun(() -> mailUtil.sendMail("TrialApply", trialApply));
            commonUtils.asyncRun(() -> mailUtil.sendMail("TrialApplyStaff", trialApply));
        }
        return res;
    }

    @Override
    public List<TrialApply> getTrialApplies(TrialApplyGetParam trialApplyGetParam) {
        trialApplyGetParam.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        trialApplyGetParam.setStart((trialApplyGetParam.getPage() - 1) * trialApplyGetParam.getSize());
        return trialDao.getTrialApplies(trialApplyGetParam);
    }

    @Override
    public int getTrialAppliesCount(TrialApplyGetParam trialApplyGetParam) {
        trialApplyGetParam.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        return trialDao.getTrialAppliesCount(trialApplyGetParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse openTrial(TrialOpen trialOpen) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(trialOpen, "trialOpen");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = trialOpen.getEid();
        optResponse = checkParamIsEmpty(eid, "trialOpen.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        Long sid = RequestUtil.getEsHeaderSid(defaultSid);

        // 合約處理
        this.SaveTenantContractFromTrial(trialOpen, sid, eid);

        trialOpen.setSid(sid);
        Long trialOpenId = trialOpen.getId();
        if (LongUtil.isEmpty(trialOpenId)) {
            trialOpen.setId(SnowFlake.getInstance().newId());
        } else {
            trialDao.deleteTrialOpenModuleContractMap(trialOpenId);
        }
        List<TrialOpenModuleContractMap> trialOpenModuleContractMapList =
                trialOpen.getTrialOpenModuleContractMaps();
        if (!CollectionUtils.isEmpty(trialOpenModuleContractMapList)) {
            //检查eid下是否已经存在同模组且有效的合约，存在就不允许开通
            //避免出现多笔租户模组合约纪录，导致授权无法确切决定租户模组合约Id
            // 20221129 任務5126 改成訂閱模組存在時對訂閱模組做更新
            /*BaseResponse response = tenantService.checkExistTenantContractModule(sid, eid, trialOpenModuleContractMapList.stream().filter(x -> x != null)
                    .map(x -> {
                        //考虑到，有可能ModuleId没取到值，尝试往实际运维商运维模组对象取
                        Long moduleId = x.getModuleId();
                        if (!LongUtil.isEmpty(moduleId)) {
                            return moduleId;
                        }
                        SupplierAiopsModule sam = x.getSupplierAiopsModule();
                        if (sam == null) {
                            return 0L;
                        }
                        return sam.getId();
                    }).collect(Collectors.toList()));
            if (!response.checkIsSuccess()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return response;
            }*/
            List<TrialModuleContract> trialModuleContracts = new ArrayList<>();
            List<TrialModuleContractDetail> trialModuleContractDetails = new ArrayList<>();
            List<TenantModuleContract> tenantModuleContracts = new ArrayList<>();
            List<TenantModuleContractDetail> tenantModuleContractDetails = new ArrayList<>();
            List<TenantModuleContractHistory> tenantModuleContractHistories = new ArrayList<>();
            trialOpenModuleContractMapList.stream().forEach(o -> {
                o.setId(SnowFlake.getInstance().newId());
                o.setOpenId(trialOpen.getId());
                TrialModuleContract trialModuleContract = o.getTrialModuleContract();
                if (ObjectUtils.isEmpty(trialModuleContract)) {
                    return;
                }
                //处理试用模组合约
                trialModuleContract.setId(SnowFlake.getInstance().newId());
                trialModuleContract.setSid(sid);
                o.setContractId(trialModuleContract.getId());
                trialModuleContracts.add(trialModuleContract);

                //处理租户模组合约
                TenantModuleContract tenantModuleContract = new TenantModuleContract();
                BeanUtils.copyProperties(trialModuleContract, tenantModuleContract);
                tenantModuleContract.setEid(eid);
                tenantModuleContract.setStaffId(trialOpen.getStaffId());
                //檢查租戶模組是否存在
                List<TenantModuleContract> tenantModuleContractList = tenantService.getTenantModuleContractsByServiceCode(trialOpen.getServiceCode(), trialModuleContract.getModuleId());
                if (!CollectionUtils.isEmpty(tenantModuleContractList)) {
                    //huly: 修复漏洞/bug == 改成equals
                    TenantModuleContract oldTenantModuleContract = tenantModuleContractList.stream().filter(e -> e.getModuleId().equals(trialModuleContract.getModuleId())).findFirst().orElse(null);
                    if (!ObjectUtils.isEmpty(oldTenantModuleContract)) {
                        tenantModuleContract.setId(oldTenantModuleContract.getId());
                    } else {
                        tenantModuleContract.setId(SnowFlake.getInstance().newId());
                    }
                } else {
                    tenantModuleContract.setId(SnowFlake.getInstance().newId());
                }
                tenantModuleContract.setStatus(ModuleContractStatus.TRIALING.getIndex());
                tenantModuleContract.setOpenId(trialOpen.getId());
                tenantModuleContract.setUserName(trialOpen.getUserName());
                tenantModuleContract.setNoticeEmail(trialOpen.getNoticeEmail());
                tenantModuleContracts.add(tenantModuleContract);

                //处理租户模组合约历史
                TenantModuleContractHistory tenantModuleContractHistory = new TenantModuleContractHistory();
                BeanUtils.copyProperties(tenantModuleContract, tenantModuleContractHistory);
                tenantModuleContractHistory.setId(SnowFlake.getInstance().newId());
                tenantModuleContractHistory.setModuleContractId(tenantModuleContract.getId());
                tenantModuleContractHistories.add(tenantModuleContractHistory);

                //处理试用模组合约明细与租户模组合约明细
                Optional<List<TrialModuleContractDetail>> optTrialModuleContractDetailList =
                        processModuleContractDetail(trialModuleContract.getId(),
                                trialModuleContract.getTrialModuleContractDetailList());
                if (optTrialModuleContractDetailList.isPresent()) {
                    List<TrialModuleContractDetail> trialModuleContractDetailList =
                            optTrialModuleContractDetailList.get();
                    trialModuleContractDetails.addAll(trialModuleContractDetailList);

                    Optional<List<TenantModuleContractDetail>> optTenantModuleContractDetailList =
                            createModuleContractDetailListBySource(trialModuleContractDetailList,
                                    TenantModuleContractDetail.class, tenantModuleContract.getId());
                    if (optTenantModuleContractDetailList.isPresent()) {
                        tenantModuleContractDetails.addAll(optTenantModuleContractDetailList.get());
                    }
                }
            });
            trialDao.saveTrialOpenModuleContractMap(trialOpen.getTrialOpenModuleContractMaps());
            if (!CollectionUtils.isEmpty(trialModuleContracts)) {
                trialDao.saveTrialModuleContracts(trialModuleContracts);
                if (!CollectionUtils.isEmpty(trialModuleContractDetails)) {
                    trialDao.saveTrialModuleContractDetailList(trialModuleContractDetails);
                }
                tenantDao.saveTenantModuleContracts(tenantModuleContracts); //update
                if (!CollectionUtils.isEmpty(tenantModuleContractDetails)) {
                    tenantDao.saveTenantModuleContractDetailList(tenantModuleContractDetails);
                }
                tenantService.setTenantModuleContractHistoriesOperateType(tenantModuleContracts, tenantModuleContractHistories);
                tenantDao.saveTenantModuleContractHistories(tenantModuleContractHistories);
            }
            String appId = RequestUtil.getHeaderFuncAppId();
            CompletableFuture.runAsync(() -> {
                try {
                    acpUtil.callAcpOpenModule(appId, trialOpen.getServiceCode(), tenantModuleContracts);
                } catch (Exception e) {
                    log.error("callAcpOpenModule error", e);
                }
            });
        }
        trialDao.updateApplyStatus(trialOpen.getApplyId(), ApplyStatus.OPENED.getIndex());
        trialDao.updateInstallStatus(trialOpen.getApplyId(), InstallStatus.INSTALLING.getIndex());

        trialDao.openTrial(trialOpen);
        return BaseResponse.ok(trialOpen.getId());
    }

    private <T extends ModuleContractDetailBase> Optional<List<T>> processModuleContractDetail(
            Long parentId, List<T> oriList) {
        if (CollectionUtils.isEmpty(oriList)) {
            return Optional.empty();
        }
        oriList.stream().filter(x -> x != null).forEach(x -> {
            x.setId(SnowFlake.getInstance().newId());
            x.setTmcId(parentId);
            x.setUsedCount(0);
        });
        return Optional.of(oriList);
    }

    private <T, R extends ModuleContractDetailBase> Optional<List<R>> createModuleContractDetailListBySource(
            List<T> sourceList, Class<R> targetClass, Long targetParentId) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Optional.empty();
        }
        List<R> resultList = new ArrayList<>(sourceList.size());
        sourceList.stream().filter(x -> x != null).forEach(x -> {
            try {
                R targetItem = targetClass.newInstance();
                BeanUtils.copyProperties(x, targetItem);
                targetItem.setId(SnowFlake.getInstance().newId());
                targetItem.setTmcId(targetParentId);
                resultList.add(targetItem);
            } catch (Exception ex) {
                return;
            }
        });
        return Optional.of(resultList);
    }

    @Override
    public boolean saveInstallInfo(TrialOpen trialOpen) {
        boolean res = trialDao.saveInstallInfo(trialOpen) > 0;
        if (res) {
            //寄送邮件
            commonUtils.asyncRun(() -> mailUtil.sendMail("TrialOpen", trialOpen));
        }
        return res;
    }

    @Override
    public TrialOpen getTrialOpenDetail(long applyId) {
        return trialDao.getTrialOpenDetail(RequestUtil.getEsHeaderSid(defaultSid), applyId);
    }

    @Override
    public BaseResponse getTrialModuleContractEmptyList() {
        //只获取试用可选择的模组
        BaseResponse response = supplierAiopsService.getModuleAndClass(true);
        Optional<List<SupplierAiopsModule>> optSupplierAiopsModuleList = response.checkAndGetCurrentData();
        if (!optSupplierAiopsModuleList.isPresent()) {
            return response;
        }
        List<SupplierAiopsModule> supplierAiopsModuleList = optSupplierAiopsModuleList.get();
        if (CollectionUtils.isEmpty(supplierAiopsModuleList)) {
            return BaseResponse.ok(new ArrayList<>(0));
        }
        //创建新的试用模组合约，拷贝模组与类别数据，填充默认值
        List<TrialModuleContract> trialModuleContractList = supplierAiopsModuleList.stream()
                .filter(x -> x != null)
                .map(x -> {
                    TrialModuleContract tmc = new TrialModuleContract();
                    BeanUtils.copyProperties(x, tmc);
                    tmc.setId(0L);
                    tmc.setModuleId(x.getId());
                    tmc.setDateCount(0);
                    List<SupplierAiopsModuleClass> samcList = x.getSupplierAiopsModuleClassList();
                    if (!CollectionUtils.isEmpty(samcList)) {
                        tmc.setTrialModuleContractDetailList(samcList.stream().filter(y -> y != null).map(y -> {
                            TrialModuleContractDetail tmcd = new TrialModuleContractDetail();
                            BeanUtils.copyProperties(y, tmcd);
                            tmcd.setId(0L);
                            tmcd.setTmcId(0L);
                            tmcd.setSamcId(y.getId());
                            tmcd.setAvailableCount(0);
                            tmcd.setUsedCount(0);
                            return tmcd;
                        }).collect(Collectors.toList()));
                    }
                    List<SupplierAiopsModuleItem> supplierAiopsModuleItems = x.getSupplierAiopsModuleItems();
                    if (!CollectionUtils.isEmpty(supplierAiopsModuleItems)) {
                        tmc.setItemNo(supplierAiopsModuleItems.get(0).getItemNo());
                    }
                    return tmc;
                })
                .collect(Collectors.toList());
        return BaseResponse.ok(trialModuleContractList);
    }

    @Override
    public boolean installSuccess(long applyId, Date installTime) {
        return trialDao.installSuccess(applyId, installTime) > 0;
    }

    @Override
    public boolean installFail(long applyId, String failReason) {
        return trialDao.installFail(applyId, failReason) > 0;
    }

    @Override
    public List<TrialApplyRejectMap> getTrialApplyRejectReasons(long applyId) {
        return trialDao.getTrialApplyRejectReasons(applyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRejectTrialApply(List<TrialApplyRejectMap> trialApplyRejectMaps) {
        if (CollectionUtils.isEmpty(trialApplyRejectMaps)) {
            return false;
        }
        trialApplyRejectMaps = trialApplyRejectMaps.stream().peek(o -> {
            o.setSid(RequestUtil.getEsHeaderSid(defaultSid));
            o.setId(SnowFlake.getInstance().newId());
        }).collect(Collectors.toList());
        trialDao.updateApplyStatus(trialApplyRejectMaps.get(0).getApplyId(), ApplyStatus.REJECTED.getIndex());
        trialDao.updateInstallStatus(trialApplyRejectMaps.get(0).getApplyId(), InstallStatus.UNINSTALL.getIndex());
        trialDao.deleteTrialApplyRejectMap(trialApplyRejectMaps.get(0).getApplyId());
        return trialDao.saveRejectTrialApply(trialApplyRejectMaps) > 0;
    }

    @Override
    public List<TrialApplyRejectReason> getRejectReasons() {
        return trialDao.getRejectReasons(RequestUtil.getEsHeaderSid(defaultSid));
    }

    @Override
    public void sendModuleContractExpireMail(Long specifyCheckEid) {
        if (CollectionUtils.isEmpty(remainDays)) {
            log.warn("remainDays is empty.");
            return;
        }
        // 因 Http 上下文會斷鍊, 故增加一個擴充, 來產生必要參數
        long sid = RequestUtil.getHeaderSid();
        String token = StringUtils.isEmpty(RequestUtil.getHeaderToken()) ?
                iamService.grantAccessToken(verifyUserId, verifytenantId) : RequestUtil.getHeaderToken();
        String appId = RequestUtil.getHeaderFuncAppId();
        Supplier<Map<String, Object>> supplierOtherInfo = () -> {
            Map<String, Object> otherInfo = new HashMap<>();
            otherInfo.put("sid", sid);
            otherInfo.put("token", token);
            otherInfo.put("appId", appId);
            return otherInfo;
        };
        commonUtils.asyncRun(() -> {
            remainDays.stream().forEach(remainDay -> {
                //查询试用
                remainCore(remainDay,
                        ModuleContractStatus.TRIALING,
                        ModuleContractStatus.TRIAL_EXPIRED,
                        "TrialExpired", "TrialNotExpired",
                        sid, supplierOtherInfo, specifyCheckEid);

                //查询订阅
                remainCore(remainDay, 
                        ModuleContractStatus.SUBSCRIBED,
                        ModuleContractStatus.SUBSCRIBED_EXPIRED,
                        "SubExpired", "SubNotExpired",
                        sid, supplierOtherInfo, specifyCheckEid);
                //查询年维
                if (serviceArea.equalsIgnoreCase("cn")) {
                    remainCore(remainDay, 
                            ModuleContractStatus.ANNUAL_MAINTENANCE_SERVICES, 
                            ModuleContractStatus.ANNUAL_MAINTENANCE_EXPIRED,
                            "AnnualMaintenanceExpired", "AnnualMaintenanceNotExpired",
                            sid, supplierOtherInfo, specifyCheckEid);
                }
            });
        });
    }

    private void remainCore(int remainDay, ModuleContractStatus findStatus, ModuleContractStatus expiredStatus,
                            String expiredMailType, String notExpiredMailType,
                            Long sid, Supplier<Map<String, Object>> supplierOtherInfo,
                            Long specifyCheckEid
    ) {
        log.info(
                "[sendModuleContractExpireMail][remainCore] mailType:{}, notExpiredMailType:{}, remainDay:{}, start run.",
                expiredMailType, notExpiredMailType, remainDay);
        List<Tenant> tenants = tenantDao.getUnExpiredTenantModuleContracts(
                remainDay, findStatus.getIndex(), specifyCheckEid, this.checkExpirationModuleIds);
        if (CollectionUtils.isEmpty(tenants)) {
            return;
        }
        log.info(
                "[sendModuleContractExpireMail][remainCore] found:{} tenants for mailType:{}, notExpiredMailType:{}, remainDay:{}",
                tenants.size(), expiredMailType, notExpiredMailType, remainDay);

        TenantTpParams tenantTpParams = new TenantTpParams();
        tenantTpParams.setSid(sid);
        tenantTpParams.setTpCode("ASIA_INFO");
        Map<Long, TenantTp> eidTenantTpMap = tenantDao.selectAllAsiaTenant(tenantTpParams).stream()
                .collect(Collectors.toMap(TenantTp::getEid, Function.identity()));

        tenants.forEach(tenant -> {
            if (remainDay < 0) {
                List<TenantModuleContract> tmcList = tenant.getTenantModuleContracts();
                //查找产品通知信息必须在更新合约状态之前
                setProductNoticeInfo(tmcList);
                updateTenantModuleContractStatus(tmcList, expiredStatus.getIndex());
                mailUtil.sendMail(expiredMailType, tenant, supplierOtherInfo);

                //特殊处理亚信合约
                if (eidTenantTpMap.containsKey(tenant.getSid())) {
                    tmcList.forEach(tmc -> tenantService.asiaInfoTenantDeal(tmc, false));
                }
                return;
            }
            NotExpiredTenant notExpiredTenant = new NotExpiredTenant();
            BeanUtils.copyProperties(tenant, notExpiredTenant);
            //因为remainDay为0时还算有授权，因此通知日要加1
            notExpiredTenant.setRemainDayCount(remainDay + 1);
            mailUtil.sendMail(notExpiredMailType, notExpiredTenant, supplierOtherInfo);
        });
    }

    private void setProductNoticeInfo(List<TenantModuleContract> tmcList) {
        if (CollectionUtils.isEmpty(tmcList)) {
            return;
        }
        //透过tmcdId查找租户合约相关联的产品应用代号，后面通知使用
        tmcList.stream().filter(Objects::nonNull)
                .map(x -> Pair.of(x, x.getTenantModuleContractDetailList()))
                .filter(x -> CollectionUtil.isNotEmpty(x.getRight()))
                .map(x -> Pair.of(x.getLeft(), x.getRight().stream().map(TenantModuleContractDetail::getId)
                        .filter(LongUtil::isNotEmpty).collect(Collectors.toList())))
                .filter(x -> CollectionUtil.isNotEmpty(x.getRight()))
                .forEach(x -> {
                    List<Long> tmcdIdList = x.getRight();
                    BaseResponse<List<ProductAppCodeMapping>> res =
                            aioItmsFeignClient.getProductAppCodeMapListByTmcdIdList(tmcdIdList);
                    if (res.checkIsSuccess()) {
                        x.getLeft().setProductAppCodeMapList(res.getData());
                    } else {
                        log.error("setProductNoticeInfo getProductAppCodeMapListByTmcdIdList:{} error:{}",
                                tmcdIdList.stream().map(LongUtil::safeToString)
                                        .collect(Collectors.joining(",")), res.getErrMsg());
                    }
                });
    }

    private void updateTenantModuleContractStatus(List<TenantModuleContract> tenantModuleContracts, int status) {
        if (CollectionUtils.isEmpty(tenantModuleContracts)) {
            return;
        }
        //修改状态为已到期
        List<Long> idList = tenantModuleContracts.stream()
                .filter(x -> x != null)
                .map(tenantModuleContract -> tenantModuleContract.getId())
                .filter(x -> !LongUtil.isEmpty(x))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        tenantDao.updateTenantModuleContractStatus(idList, status, ModuleOrderMethod.CHANGE_AUTH.getIndex());
        //保存操作历史
        List<TenantModuleContractHistory> tenantModuleContractHistories = new ArrayList<>();
        tenantModuleContracts.stream().forEach(tenantModuleContract -> {
            TenantModuleContractHistory tenantModuleContractHistory =
                    new TenantModuleContractHistory(tenantModuleContract.getId(), "sync");
            BeanUtils.copyProperties(tenantModuleContract, tenantModuleContractHistory);
            tenantModuleContractHistory.setId(SnowFlake.getInstance().newId());
            tenantModuleContractHistory.setStatus(status);
            tenantModuleContractHistory.setOrderMethod(ModuleOrderMethod.CHANGE_AUTH.getIndex());
            tenantModuleContractHistories.add(tenantModuleContractHistory);
        });
        //处理对应的实例授权过期
        tenantService.stopAiopsInstanceAuthStatusByTmcIdList(idList);
        tenantService.setTenantModuleContractHistoriesOperateType(tenantModuleContracts, tenantModuleContractHistories);
        tenantDao.saveTenantModuleContractHistories(tenantModuleContractHistories);
    }

    @Override
    public List<TrialApplySource> getTrialApplySources(Integer sourceType, int page, int size) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sourceType", sourceType);
        map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
        int start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return trialDao.getTrialApplySources(map);
    }

    @Override
    public int getTrialApplySourcesCount(Integer sourceType) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sourceType", sourceType);
        map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
        return trialDao.getTrialApplySourcesCount(map);
    }

    @Override
    public List<TrialApplySource> getTrialApplyActiveList(String themeModelCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("themeModelCode", themeModelCode);
        map.put("sid", RequestUtil.getEsHeaderSid(defaultSid));
        return trialDao.getTrialApplyActiveList(map);
    }

    @Override
    public ResponseBase saveTrialApplySource(TrialApplySource trialApplySource) {
        trialApplySource.setSid(RequestUtil.getEsHeaderSid(defaultSid));
        HashMap<String, Object> map = new HashMap<>();
        map.put("col", "sourceCode");
        map.put("value", trialApplySource.getSourceCode());
        if (trialApplySource.getId() == 0) {
            map.put("mode", "insert");
            Integer sourceByCode = trialDao.getTrialApplySourceByCol(map);
            if (sourceByCode != null) {
                return ResponseBase.error(ResponseCode.TRIAL_APPLY_SOURCE_CODE_EXIST);
            }
            trialApplySource.setId(SnowFlake.getInstance().newId());
            trialApplySource.setActivityTime(new Date());
        } else {
            map.put("mode", "update");
            map.put("id", trialApplySource.getId());
            Integer sourceByCode = trialDao.getTrialApplySourceByCol(map);
            if (sourceByCode != null) {
                return ResponseBase.error(ResponseCode.TRIAL_APPLY_SOURCE_CODE_EXIST);
            }
        }
        return ResponseBase.ok(trialDao.saveTrialApplySource(trialApplySource) > 0);
    }

    private void SaveTenantContractFromTrial(TrialOpen trialOpen, long sid, long eid) {
        //當有產品線163、15時以163為主。當只有163或15時就以有的為主。當都沒有時以163為主做建立或更新
        // 有合約且到期，則更新合約。合約狀態:台灣區為C，大陸區為C0。合約日期為試用時間。isTrial要為1
        // 有合約但未到期，則不做合約處理
        // 無合約，則新增試用合約。合約狀態:台灣區為C，大陸區為C0。合約日期為試用時間。isTrial要為1

        // 找出已訂閱之模組中最大與最小日期
        List<TenantModuleContract> tenantModules = new ArrayList<>();
        tenantModules = tenantService.getTenantModuleContractsByServiceCode(trialOpen.getServiceCode(), null);
        Date minStartDate = null;
        Date maxEndDate = null;
        Date minTrialOpenStartDate = null;
        Date maxTrialOpenEndDate = null;
        TenantModuleContract minStartDateTenantModule = null;
        TenantModuleContract maxEndDateTenantModule = null;
        tenantModules = tenantModules.stream().filter(Objects::nonNull).filter(x -> x.getId() != null).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(tenantModules)) {
            minStartDateTenantModule = tenantModules
                    .stream().min(Comparator.comparing(TenantModuleContract::getStartDate))
                    .orElse(null);
            maxEndDateTenantModule = tenantModules
                    .stream().max(Comparator.comparing(TenantModuleContract::getEndDate))
                    .orElse(null);
        }
        if (!ObjectUtils.isEmpty(minStartDateTenantModule)) {
            minStartDate = minStartDateTenantModule.getStartDate();
        }
        if (!ObjectUtils.isEmpty(maxEndDateTenantModule)) {
            maxEndDate = maxEndDateTenantModule.getEndDate();
        }
        // 找出開通試用所選擇訂閱模組中最大最小日期
        TrialOpenModuleContractMap minStartDateTrialOpenModule = trialOpen.getTrialOpenModuleContractMaps()
                .stream().min(Comparator.comparing(e -> e.getTrialModuleContract().getStartDate()))
                .orElseThrow(NoSuchElementException::new);
        TrialOpenModuleContractMap maxEndDateTrialOpenModule = trialOpen.getTrialOpenModuleContractMaps()
                .stream().max(Comparator.comparing(e -> e.getTrialModuleContract().getEndDate()))
                .orElseThrow(NoSuchElementException::new);

        if (!ObjectUtils.isEmpty(minStartDateTrialOpenModule)) {
            minTrialOpenStartDate = minStartDateTrialOpenModule.getTrialModuleContract().getStartDate();
        }
        if (!ObjectUtils.isEmpty(maxEndDateTrialOpenModule)) {
            maxTrialOpenEndDate = maxEndDateTrialOpenModule.getTrialModuleContract().getEndDate();
        }
        // 比較找出最大與最小日期
        if (minStartDate != null && minTrialOpenStartDate != null) {
            minStartDate = minStartDate.compareTo(minTrialOpenStartDate) > 0 ? minTrialOpenStartDate : minStartDate;
        } else if (minTrialOpenStartDate != null) {
            minStartDate = minTrialOpenStartDate;
        }

        if (maxEndDate != null && maxTrialOpenEndDate != null) {
            maxEndDate = maxEndDate.compareTo(maxTrialOpenEndDate) < 0 ? maxTrialOpenEndDate : maxEndDate;
        } else if (maxTrialOpenEndDate != null) {
            maxEndDate = maxTrialOpenEndDate;
        }

        // 檢查合約163、15/147
        String defaultProductCode = "TW".equals(serviceArea) ? "163" : "147";
        TenantContract tenant_Contract = tenantDao.getTenantContract(sid, eid, defaultProductCode, null, null);
        if ("TW".equals(serviceArea)) {
            if (ObjectUtils.isEmpty(tenant_Contract)) {
                defaultProductCode = "15";
                tenant_Contract = tenantDao.getTenantContract(sid, eid, defaultProductCode, true, null);
                if (ObjectUtils.isEmpty(tenant_Contract)) {
                    defaultProductCode = "163";
                }
            }
        }
        String productName = tenantDao.getProductName(sid, defaultProductCode);
        if (ObjectUtils.isEmpty(tenant_Contract)) {
            // 新增試用合約
            TenantContract insertTenantContract = new TenantContract();
            insertTenantContract.setId(SnowFlake.getInstance().newId());
            insertTenantContract.setSid(sid);
            insertTenantContract.setEid(eid);
            insertTenantContract.setProductCode(defaultProductCode);
            insertTenantContract.setServiceStaffId(trialOpen.getStaffId());

            insertTenantContract.setProductShortName(productName);
            if ("CN".equals(serviceArea)) {
                insertTenantContract.setContractState("C0");
            } else {
                insertTenantContract.setContractState("C");
            }
            insertTenantContract.setContractStartDate(minStartDate);
            insertTenantContract.setContractExpiryDate(maxEndDate);
            insertTenantContract.setIsTrial(true);
            tenantDao.insertTenantContract(insertTenantContract);

            HashMap<String, Object> map = new HashMap<>();
            map.put("escloudDBName", escloudDBName);
            map.put("customerServiceCode", trialOpen.getServiceCode());
            map.put("productCode", defaultProductCode);
            map.put("productCategory", productName);
            DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            if ("CN".equals(serviceArea)) {
                map.put("contractState", "C0");
            } else {
                map.put("contractState", "C");
            }
            map.put("contractStartDate", dateFormat.format(minStartDate));
            map.put("contractExprityDate", dateFormat.format(maxEndDate));
            map.put("IsTrial", true);
            map.put("contractSource", "3"); // 表示為試用新增
            tenantDao.saveCustomerService(map);// 對escloud-db中的合約資料表作新增或更新

        } else {
            // 檢查合約是否到期
            boolean isUpdateContract = false;
            if (tenant_Contract.getContractExpiryDate() != null) {
                //DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date nowDate = new Date();
                if (nowDate.compareTo(tenant_Contract.getContractExpiryDate()) > 0) {
                    // 表示合約已到期，所以可以更新合約
                    isUpdateContract = true;
                } else {
                    // 合約未到期，所以不做合約處理
                    isUpdateContract = false;
                }
            } else {
                isUpdateContract = true;
            }
            if (isUpdateContract) {
                // 更新合約。合約狀態:台灣區為C，大陸區為C0。合約日期為試用時間。isTrial要為1
                TenantContract updateTenantContract = tenant_Contract;
                updateTenantContract.setIsTrial(true);
                if ("CN".equals(serviceArea)) {
                    updateTenantContract.setContractState("C0");
                } else {
                    updateTenantContract.setContractState("C");
                }
                updateTenantContract.setContractStartDate(minStartDate);
                updateTenantContract.setContractExpiryDate(maxEndDate);

                //更新aio-db
                tenantDao.updateTenantContract(updateTenantContract);

                HashMap<String, Object> map = new HashMap<>();
                map.put("escloudDBName", escloudDBName);
                map.put("customerServiceCode", trialOpen.getServiceCode());
                map.put("productCode", defaultProductCode);
                map.put("productCategory", productName);
                DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                if ("CN".equals(serviceArea)) {
                    map.put("contractState", "C0");
                } else {
                    map.put("contractState", "C");
                }
                map.put("contractStartDate", dateFormat.format(minStartDate));
                map.put("contractExprityDate", dateFormat.format(maxEndDate));
                map.put("isTrial", true);
                map.put("contractSource", "3"); // 表示為試用新增
                tenantDao.saveCustomerService(map);// 對escloud-db中的合約資料表作新增或更新
            }
        }
    }
}
