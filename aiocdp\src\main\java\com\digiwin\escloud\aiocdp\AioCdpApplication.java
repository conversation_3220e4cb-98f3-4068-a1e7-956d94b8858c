package com.digiwin.escloud.aiocdp;

import com.digiwin.escloud.common.swagger.EnableSwagger3;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.RestController;

@EnableScheduling
@EnableAsync
@RestController
@EnableDiscoveryClient
@SpringBootApplication
@MapperScan({"com.digiwin.escloud.aiocdp.*.dao"})
@ComponentScan(basePackages = {"com.digiwin.escloud.aiocdp", "com.digiwin.escloud.mybatis"})
@EnableFeignClients(basePackages = {"com.digiwin.escloud.common.feign"})
@EnableSwagger3
public class AioCdpApplication {
    public static void main(String[] args) {
        System.setProperty("nacos.logging.default.config.enabled","false");
        new SpringApplicationBuilder(AioCdpApplication.class).run(args);
    }
}
