package com.digiwin.escloud.aiouser.dao;

import com.digiwin.escloud.aiouser.model.customer.CustomerServiceInfo;
import com.digiwin.escloud.aiouser.model.module.ModuleQryReq;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModule;
import com.digiwin.escloud.aiouser.model.supplier.SupplierTenantMap;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.aiouser.model.tenant.*;
import com.digiwin.escloud.aiouser.model.user.ProductLine147NoticeMailParams;
import com.digiwin.escloud.aiouser.mybatisenums.OperateType;
import com.digiwin.escloud.common.model.TenantTpParams;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.mybatis.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;

@Mapper
public interface ITenantDao {
    List<Tenant> getTenants(HashMap<String, Object> map);

    List<TenantInfo> getTenantList(HashMap<String, Object> map);

    List<Long> getTenantSids(HashMap<String, Object> map);

    long getTenantsCount(HashMap<String, Object> map);

    Tenant getTenantDetail(@Param("tenantSid") long tenantSid, @Param("sid") long sid);

    List<Tenant> getTenantDetailV2(@Param("tenantList") List<ProductLine147NoticeMailParams.TenantModuleInfo> tenantSid);

    TenantContract getTenantContract(@Param("sid") long sid,
                                     @Param("eid") long eid,
                                     @Param("productCode") String productCode,
                                     @Param("needUnExpired") Boolean needUnExpired,
                                     @Param("contractStatus")String contractStatus);

    void updateTCProductIdByProductCode(@Param("sid") long sid,
                                        @Param("eid") long eid,
                                        @Param("productCode") String productCode,
                                        @Param("productId") String productId
    );

    /**
     * 查询租户信息
     *
     * @param tenantSid
     * @return
     */
    Tenant getTenant(@Param("sid") long tenantSid);


    NotExpiredTenant getTenantV2(@Param("sid") long tenantSid);

    /**
     * 根据客服代号查找租户信息
     *
     * @param serviceCode
     * @return
     */
    Tenant getTenantByServiceCode(@Param("serviceCode") String serviceCode);

    /**
     * 根据客服代号查找租户信息,可能存在客代前面有flag，所以需要模糊查
     *
     * @param serviceCode
     * @return
     */
    Tenant getTenantDetailByServiceCode(@Param("serviceCode") String serviceCode);

    /**
     * 查询租户产品合约列表
     *
     * @param sid 运维商Id
     * @param eid 租户Id
     * @return 租户产品合约列表
     */
    List<TenantContract> selectTenantProductContractList(@Param("sid") long sid, @Param("eid") long eid);

    List<Tenant> getTenantSimpleInfos(HashMap<String, Object> map);

    long getTenantSimpleCount(HashMap<String, Object> map);

    List<User> getTenantUsers(HashMap<String, Object> map);

    long getTenantUsersCount(@Param("tenantSid") long tenantSid, @Param("userName") String userName);

    /**
     * 更新租户描述信息
     *
     * @param eid         租户Id
     * @param productCode 产品别
     * @param description 描述内容
     * @return 影响笔数
     */
    int updateTenant(@Param("eid") Long eid, @Param("productCode") String productCode, @Param("description") String description);

    List<ContractState> getContractStateList();

    int insertTenant(Tenant tenant);

    /**
     * 保存租户合约信息
     *
     * @param tenantContract
     * @return
     */
    int insertTenantContract(TenantContract tenantContract);

    /**
     * 更新租户合约信息
     *
     * @param tenantContract
     * @return
     */
    int updateTenantContract(TenantContract tenantContract);

    @InterceptorIgnore(dataPermission = false)
    List<TenantInfo> getSearchTenantList(HashMap<String, Object> map);

    /**
     * 查询租户Id
     *
     * @param tenantId 租户Id
     * @return 租户Id
     */
    Long selectTenantId(Long tenantId);

    /**
     * 查询租户名称列表
     *
     * @param tenantIdList 租户Id列表
     * @return 租户名称列表
     */
    List<TenantNameInfo> selectTenantNameList(@Param("tenantIdList") List<Long> tenantIdList);

    List<TenantMapDTO> selectTenantNameAndServiceCodeList(@Param("tenantIdList") List<Long> tenantIdList);

    List<Long> getAuthorizedTenantSids(@Param("authorizedProductCodes") List<String> authorizedProductCodes, @Param("sid") long sid);

    List<TenantInfo> getTenantExistedList(HashMap<String, Object> map);

    int getTenantExistedCount(HashMap<String, Object> map);

    Long getTenantSidByServiceCode(String serviceCode);

    List<SupplierTenantMap> getSupplierTenantMapListByMap(Map<String, Object> map);

    List<Map<String, String>> getTenantNameByServiceCode(String serviceCode);

    int getTenantModuleCountByServiceCode(HashMap<String, Object> map);

    /**
     * 查询租户模组合约列表
     *
     * @param map 条件字典
     * @return 租户模组合约列表
     */
    @InterceptorIgnore(dataPermission = false)
    List<Tenant> getTenantModuleContracts(HashMap<String, Object> map);

    /**
     * 查询某客代之模组合约
     *
     * @param map 条件字典
     * @return 某客代之模组合约
     */
    List<TenantModuleContract> getTenantModuleContractsByServiceCode(HashMap<String, Object> map);

    /**
     * 查询租户模组合约列表笔数
     *
     * @param map 条件字典
     * @return 笔数
     */
    int getTenantModuleContractsCount(HashMap<String, Object> map);

    int saveTenantModuleContracts(
            @Param("tenantModuleContracts") Collection<TenantModuleContract> tenantModuleContracts);

    int saveTenantModuleContractsBySync(
            @Param("tenantModuleContracts") Collection<TenantModuleContract> tenantModuleContracts);

    int saveTenantModuleContractHistories(List<TenantModuleContractHistory> tenantModuleContractHistories);

    int saveTenantModuleContractHistoriesBySync(List<TenantModuleContractHistory> tenantModuleContractHistories);

    /**
     * 查询特定租户模组合约授权详情
     *
     * @param tmcId 模组合约Id
     * @return 模组合约明细列表
     */
    List<TenantModuleContractDetail> selectTenantModuleContractDetailOnly(@Param("tmcId") Long tmcId);

    /**
     * 依据租户模组合约Id删除模组合约明细
     *
     * @param tmcId 租户模组合约Id
     * @return 影响笔数
     */
    int deleteTenantModuleContractDetailByTmcId(@Param("tmcId") Long tmcId);

    /**
     * 保存模组合约明细
     *
     * @param tenantModuleContractDetailList 模组合约明细列表
     * @return 影响笔数
     */
    int saveTenantModuleContractDetailList(List<TenantModuleContractDetail> tenantModuleContractDetailList);

    int saveTenantModuleContractDetailListV2(List<TenantModuleContractDetail> tenantModuleContractDetailList);

    /**
     * 依据租户模组合约明细Id列表删除模组合约明细
     *
     * @param tmcdIdList 租户模组合约明细Id列表
     * @return 影响笔数
     */
    int deleteTenantModuleContractDetailByTmcdIdList(@Param("tmcdIdList") List<Long> tmcdIdList);

    List<Tenant> getUnExpiredTenantModuleContracts(
            @Param("remainDay") int remainDay, @Param("status") int status,
            @Param("specifyCheckEid") Long specifyCheckEid,
            @Param("checkExpirationModuleIds") List<Integer> checkExpirationModuleIds
    );

    List<TenantModuleContractHistory> getTenantModuleContractHistories(long moduleContractId);

    List<TenantModuleContractHistory> selectAllTenantModuleContractHistories();

    int updateTenantModuleContractStatus(@Param("idList") List<Long> idList,
                                         @Param("status") int status,
                                         @Param("moduleOrderMethod") int moduleOrderMethod);

    /**
     * 查询存在的租户模组合约列表
     *
     * @param sid 运维商Id
     * @param eid 租户Id
     * @return 存在的租户模组合约列表
     */
    List<TenantModuleContract> selectExistTenantModuleContract(
            @Param("sid") Long sid,
            @Param("eid") Long eid,
            @Param("samIdList") Collection<Long> samIdList);

    /**
     * 查询租户运维模组授权一览
     *
     * @param sid            运维商Id
     * @param eid            租户Id
     * @param moduleCodeList 运维商运维模组代号列表
     * @return 有效的租户模组合约明细列表
     */
    List<TenantModuleContractDetail> selectTenantAllModuleContractDetail(
            @Param("sid") Long sid,
            @Param("eid") Long eid,
            @Param("moduleCodeList") List<String> moduleCodeList);

    /**
     * 依据运维项目查询租户模组合约与类别明细
     *
     * @param sid           运维商Id
     * @param eid           租户Id
     * @param aiopsItemList 运维项目列表
     * @return 租户模组合约与类别明细列表
     */
    List<TenantModuleContractDetail> selectTenantModuleContractClassDetailByAiopsItem(
            @Param("sid") Long sid,
            @Param("eid") Long eid,
            @Param("aiopsItemList") List<String> aiopsItemList);

    List<TenantModuleContractDetail> selectTenantModuleContractClassDetailByAiopsItemV2(
            @Param("sid") Long sid,
            @Param("eid") Long eid,
            @Param("marketUrl") String marketUrl,
            @Param("aiopsItemList") List<String> aiopsItemList);

    /**
     * 依据租户模组合约明细Id更新租户模组合约明细可用总数
     *
     * @param tmcdId    租户模组合约明细Id
     * @param usedCount 可用总数
     * @param isLimited 是否限制可用数量必须大于等于已用数量才可以更新
     * @return 影响笔数
     */
    Integer updateTenantModuleContractDetailUsedCountByTmcdId(
            @Param("tmcdId") Long tmcdId,
            @Param("usedCount") Integer usedCount,
            @Param("isLimited") Boolean isLimited);

    /**
     * 根据tmcdId减少可用总数
     * @param tmcdId
     * @return
     */
    Integer subTenantModuleContractDetailUsedCountByTmcdId(
            @Param("tmcdId") Long tmcdId);

    /**
     * 查询租户Id有效的运维商运维模组类别明细Id列表
     *
     * @param sid           运维商Id
     * @param eid           租户Id
     * @param aiopsItemList 运维项目列表
     * @return 运维商运维模组类别明细Id列表
     */
    List<Long> selectTenantValidSamcdIdList(
            @Param("sid") Long sid,
            @Param("eid") Long eid,
            @Param("aiopsItemList") List<String> aiopsItemList);

    /**
     * 依据租户模组合约Id列表查询租户模组合约明细Id列表
     *
     * @param tmcIdList 租户模组合约Id列表
     * @return 租户模组合约明细Id列表
     */
    List<Long> selectTmcdIdListByTmcIdList(@Param("tmcIdList") List<Long> tmcIdList);

    /**
     * 查询确切已经过期的租户模组合约明细Id
     *
     * @param tmcdIdList 租户模组合约明细Id列表
     * @return 租户模组合约明细Id列表
     */
    List<Long> selectExactExpireTmcdIdList(@Param("tmcdIdList") List<Long> tmcdIdList);

    /**
     * 依据租户Id查询租户模组合约明细字典列表
     *
     * @param sid           运维商Id
     * @param eid           租户Id
     * @param aiopsItemList 运维项目列表
     * @return 租户模组合约明细字典列表
     */
    List<Map<String, Object>> selectTmcdMapListByEid(@Param("sid") Long sid, @Param("eid") Long eid,
                                                     @Param("aiopsItemList") List<String> aiopsItemList);

    List<TenantModuleContract> getTenantHoldAuthModules(HashMap<String, Object> map);

    /**
     * 根據提供的參數確定租戶持有授權的到期狀態。
     *
     * @param map 其中包含確定租戶持有授權到期狀態所需的鍵值對
     * @return 代表授權到期狀態的tenantmodulecontract對象的列表
     *對於基於提供的輸入標準的租戶。
     */
    List<TenantModuleContract> getTenantHoldAuthExpirationStatus(HashMap<String, Object> map);

    List<TenantModuleContract> getModulesByCondition(ModuleQryReq moduleQryReq);
    /**
     * 2025-01-06 討論後新增加的
     * 根據提供的參數擷取 tenant_module_contract 表的資料, 不卡控任何授權的操作
     *
     * @param map 包含表示過濾條件或參數的鍵值對的 HashMap
     *            決定應擷取哪些 TenantModuleContract 對象
     * @return 符合指定條件的 TenantModuleContract 物件列表
     */
    List<TenantModuleContract> getTenantContractModules(HashMap<String, Object> map);

    List<String> selectAuthTmc(@Param("eid") Long eid, @Param("statusList") List<Integer> statusList);


    List<TenantModuleContract> getServiceTenantHoldAuthModules(HashMap<String, Object> map);

    String getTenantIdByServiceCode(@Param("sid") Long sid, @Param("serviceCode") String serviceCode);

    String getProductName(@Param("sid") Long sid, @Param("productCode") String productCode);

    int saveCustomerService(HashMap<String, Object> map);

    TenantModuleContract getTenantContractById(long id);

    /**
     * 依据租户模组合约明细Id列表查询租户模组合约明细占用授权关系
     *
     * @param tmcdIdList 租户模组合约明细Id列表
     * @return 租户模组合约明细占用授权关系
     */
    List<Map<String, Object>> selectTmcdHoldAuthRelateByTmcdIdList(@Param("tmcdIdList") List<Long> tmcdIdList);

    String getSupplierFlag(@Param("sid") Long sid);

    /**
     * 依据租户Sid运维商运维模组类别Id列表查询租户模组合约
     *
     * @param eid        租户Sid
     * @param samcIdList 运维商运维模组类别Id列表
     * @return 租户模组合约列表
     */
    List<TenantModuleContract> selectTmcListByEidSamcIdList(@Param("eid") Long eid,
                                                            @Param("samcIdList") Collection<Long> samcIdList);

    List<TenantModuleContractDetail> selectTmcdListByTmcIdList(@Param("tmcIdList") List<Long> tmcIdList);

    TenantTp selectAsiaTenantByEid(@Param("sid") Long sid, @Param("eid") Long eid);

    List<TenantTp> selectAllAsiaTenant(TenantTpParams params);

    //    int insertOrUpdateTenantTp(TenantTp tt);
    int insertTenantTp(TenantTp tt);

    int updateTenantTp(TenantTp tt);

    TenantTp getTenantTpByParams(@Param("tpTenantName") String tpTenantName, @Param("eid") Long eid);

    int updateTenantIsv(@Param("tenantSid") long tenantSid, @Param("isv") Boolean isv);

    List<SupplierAiopsModule> selectSupplierAiopsModule(@Param("sid") long tenantSid);

    @InterceptorIgnore(dataPermission = false)
    List<TenantModuleContract> selectTenantModuleContractByISVSid(@Param("serviceIsvSid") Long serviceIsvSid
            , @Param("sid") Long sid, @Param("eidList") List<Long> eidList);

    List<TenantTp> getTenantTpList();

    Boolean selectTenantIsNeedLogin(Long eid);

    void insertOrUpdateKitLogin(@Param("id") Long id, @Param("eid") Long eid, @Param("needLogin") Boolean isNeedLogin);

    void updateTenantModuleContractHistories(@Param("id") Long id, @Param("status") OperateType status);

    List<SubscribeTenant> getSubscribeTenantList(SubscribeTenantReq subscribeTenantReq);

    int getSubscribeTenantCnt(SubscribeTenantReq subscribeTenantReq);

    int updateTenantInstalledStatus(@Param("installed") boolean installed, @Param("tenantSid") long tenantSid);

    List<TenantModuleContract> getTenantModuleContractByModuleStatus(TenantModuleContractByStatusReq moduleContractByStatusReq);

    List<Map<Long, Integer>> getTenantModuleContractCntByModuleStatus(TenantModuleContractByStatusReq moduleContractByStatusReq);

    CustomerServiceInfo getCustomerServiceInfo(@Param("serviceCode") String serviceCode, @Param("productCode") String productCode);
}
