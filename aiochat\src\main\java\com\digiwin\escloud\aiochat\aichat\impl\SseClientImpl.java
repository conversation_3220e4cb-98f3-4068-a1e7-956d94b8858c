package com.digiwin.escloud.aiochat.aichat.impl;

import com.digiwin.escloud.aiochat.aichat.SseClient;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessage;
import com.digiwin.escloud.common.model.ResponseBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SseClientImpl implements SseClient {

    @Resource
    private ChatServiceImpl chatService;

    @Value("${aiochat.sse.timeout:2}")
    private long timeout;

//    public void callAIAgentAndAggregate(ChatMessage message) {
//
//        // 2. 调用方法获取SSE流
//        Flux<ServerSentEvent<Object>> sseFlux = chatService.invokeIndepthAIAgent(message);
//
//        // 3. 处理SSE事件流
//        Mono<String> aggregatedMessage = sseFlux
//                .takeUntil(event -> "done".equals(event.event())) // 当收到done事件时停止
//                .filter(event -> !"done".equals(event.event()))  // 过滤掉done事件本身
//                .map(event -> {
//                    // 处理事件数据（根据实际数据结构调整）
//                    if (event.data() != null) {
//                        return event.data().toString();
//                    }
//                    return "";
//                })
//                .collect(Collectors.joining()); // 聚合所有消息内容
//
//        // 4. 订阅处理结果
//        aggregatedMessage.subscribe(
//                fullMessage -> {
//                    System.out.println("完整聚合消息: " + fullMessage);
//                    // 这里可以添加后续处理逻辑
//                },
//                error -> {
//                    System.err.println("处理过程中发生错误: " + error.getMessage());
//                    // 处理错误逻辑
//                },
//                () -> System.out.println("SSE流处理完成")
//        );
//    }

    public Mono<ResponseBase> callAIAgentAndAggregateAsync(ChatMessage chatMessage) {
        return chatService.invokeIndepthAIAgent(chatMessage)
                .takeUntil(event -> "done".equals(event.event()))
                .filter(event -> !"done".equals(event.event()))
                .map(this::extractMessageContent)
                .collect(Collectors.joining())
                .map(this::filterThinkAndEnter)
                //  设置超时时间 加入配置项
                .timeout(Duration.ofMinutes(timeout))
                .map(ResponseBase::ok)
                .onErrorResume(e -> {
                    if (e instanceof TimeoutException) {
                        return Mono.just(ResponseBase.error("1","响应超时"));
                    }
                    return Mono.just(ResponseBase.error("1","处理失败"));
                });
    }

    private String extractMessageContent(ServerSentEvent<Object> event) {
        try {
            if (event.data() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) event.data();
                return data.getOrDefault("message", "").toString();
            }
            return "";
        } catch (Exception e) {
            log.error("消息解析失败:{}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 过滤掉<think>标签及其内容，并去除<br>标签
     * 由于正则默认是贪婪匹配，且.不匹配换行，所以需要加上(?s)让.匹配换行
     */
    private String filterThinkAndEnter(String message){
        if (message == null) {
            return "";
        }
        // (?s)让.匹配换行，非贪婪匹配
        message = message.replaceAll("(?s)<think>.*?</think>", "");
        message = message.replace("<br>", "");
        return message;
    }
}
