package com.digiwin.escloud.dmp.model.taggroup;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 10:23
 * @Description
 */
public enum TagRuleType {
    BASIC_INDICATORS(0,"基础指标"),
    CUSTOM(1,"自定义"),
    SCRIPT(2,"脚本"),
    IMPORTED(3,"文件导入"),
    EVENT_PREFERENCE(4,"事件偏好");

    private String msg;
    private int index;

    private TagRuleType(int index,String msg) {
        this.index = index;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public int getIndex() {
        return index;
    }
}
