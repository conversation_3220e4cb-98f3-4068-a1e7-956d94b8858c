package com.digiwin.escloud.aiocdp.industry;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocdp.industry.model.PageParams;
import com.digiwin.escloud.aiocdp.industry.model.SendMessageDto;
import com.digiwin.escloud.aiocdp.utils.ObjectSerializeUtils;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 客户控制器
 */
@RestController
@RequestMapping("/qywechat")
@Slf4j
public class TestController extends ControllerBase {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "获取云管家职能别调研问卷列表")
    @PostMapping(value = "/test")
    public BaseResponse getCustomerSurvey(@RequestParam(value = "pageNumber") Long pageNumber,
                                          @RequestParam(value = "pageSize") Long pageSize) {
        String cc="<tr><td style=\"padding-top:10px;\"><span style=\"font-size:20px;display:block;margin-bottom:10px;font-weight: 600\">【行业方案事业处】</span></td></tr><tr><td style=\"padding-top:10px;\"><span style=\"font-size:25px;text-align: center;display:block;margin-bottom:10px;font-weight: 600\">【通知】CXO症状快筛问卷调研进度总览</span></td></tr>";
        String aa = "{\n" +
                "   \"toUserEmail\" : [\"<EMAIL>\"],\n" +
                "   \"msgtype\" : \"template_card\",\n" +
                "   \"agentid\" : 1,\n" +
                "   \n" +
                "   \"enable_id_trans\": 0,\n" +
                "   \"enable_duplicate_check\": 0,\n" +
                "   \"duplicate_check_interval\": 1800,\n" +
                "    \"template_card\" : {\n" +
                "        \"card_type\" : \"news_notice\",\n" +
                "        \"source\" : {\n" +
                "            \"icon_url\": \"https://img.iplaysoft.com/wp-content/uploads/2019/free-images/free_stock_photo_2x.jpg\",\n" +
                "            \"desc\": \"企业微信\",\n" +
                "            \"desc_color\": 1 \n" +
                "        },\n" +
                "        \"action_menu\": {\n" +
                "            \"desc\": \"卡片副交互辅助文本说明\",\n" +
                "            \"action_list\": [\n" +
                "                {\"text\": \"接受推送\", \"key\": \"A\"},\n" +
                "                {\"text\": \"不再推送\", \"key\": \"B\"}\n" +
                "            ]\n" +
                "        },\n" +
                "        \"task_id\": \"task_id7\",\n" +
                "        \"main_title\" : {\n" +
                "            \"title\" : \"欢迎使用企业微信\",\n" +
                "            \"desc\" : \"您的好友正在邀请您加入企业微信\"\n" +
                "        },\n" +
                "        \"quote_area\": {\n" +
                "            \"type\": 1,\n" +
                "            \"url\": \"https://work.weixin.qq.com\",\n" +
                "            \"title\": \"企业微信的引用样式\",\n" +
                "            \"quote_text\": \"企业微信真好用呀真好用\"\n" +
                "        },\n" +
                "        \"image_text_area\": {\n" +
                "            \"type\": 1,\n" +
                "            \"url\": \"https://work.weixin.qq.com\",\n" +
                "            \"title\": \"企业微信的左图右文样式\",\n" +
                "            \"desc\": \"企业微信真好用呀真好用\",\n" +
                "            \"image_url\": \"https://img.iplaysoft.com/wp-content/uploads/2019/free-images/free_stock_photo_2x.jpg\"\n" +
                "        },\n" +
                "        \"card_image\": {\n" +
                "            \"url\": \"https://img.iplaysoft.com/wp-content/uploads/2019/free-images/free_stock_photo_2x.jpg\",\n" +
                "            \"aspect_ratio\": 1.3\n" +
                "        },\n" +
                "        \"vertical_content_list\": [\n" +
                "            {\n" +
                "                \"title\": \"惊喜红包等你来拿\",\n" +
                "                \"desc\": \"下载企业微信还能抢红包！\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"horizontal_content_list\" : [\n" +
                "            {\n" +
                "                \"keyname\": \"邀请人\",\n" +
                "                \"value\": \"徐无鬼\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"type\": 1,\n" +
                "                \"keyname\": \"企业微信官网\",\n" +
                "                \"value\": \"点击访问\",\n" +
                "                \"url\": \"https://work.weixin.qq.com\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"type\": 3,\n" +
                "                \"keyname\": \"员工信息\",\n" +
                "                \"value\": \"点击查看\",\n" +
                "                \"userid\": \"230DE3FE4281024D9657B7193FAFC1A7\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"jump_list\" : [\n" +
                "            {\n" +
                "                \"type\": 1,\n" +
                "                \"title\": \"企业微信官网\",\n" +
                "                \"url\": \"https://work.weixin.qq.com\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"card_action\": {\n" +
                "            \"type\": 1,\n" +
                "            \"url\": \"https://work.weixin.qq.com\",\n" +
                "            \"appid\": \"小程序的appid\",\n" +
                "            \"pagepath\": \"/index.html\"\n" +
                "        }\n" +
                "    }\n" +
                "}";
        String bb = "{\n" +
                "   \"toUserEmail\" : [\"<EMAIL>\"],\n" +
                "   \"msgtype\" : \"news\",\n" +
                "   \"news\" : {\n" +
                "       \"articles\" : [\n" +
                "           {\n" +
                "               \"title\" : \"中秋节礼品领取\",\n" +
                "               \"description\" : \"今年中秋节公司有豪礼相送\",\n" +
                "               \"url\" : \"URL\",\n" +
                "               \"picurl\" : \"https://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png\"\n" +
                "           }\n" +
                "        ]\n" +
                "   },\n" +
                "   \"enable_id_trans\": 0,\n" +
                "   \"enable_duplicate_check\": 0,\n" +
                "   \"duplicate_check_interval\": 1800\n" +
                "}";
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        long timestamp = System.currentTimeMillis();
        SendMessageDto messageDto = JSON.parseObject(bb, SendMessageDto.class);
        PageParams pageParams = new PageParams(pageNumber, pageSize);
//        String signature = SignClient.getSignature(publicFieldParam, timestamp);
        String sortStr = ObjectSerializeUtils.generateObject(pageParams);
        String md5 = DigestUtils.md5Hex((sortStr + "&timestamp=" + timestamp).getBytes(StandardCharsets.UTF_8));

        headers.set("timestamp", timestamp + "");
//        headers.set("signature", md5);
        headers.set("signature", md5);

//        HttpEntity<SendMessageDto> requestEntity = new HttpEntity<>(messageDto, headers);
        HttpEntity<PageParams> requestEntity = new HttpEntity<>(pageParams, headers);
        System.out.println(JSON.toJSONString(requestEntity));
//        String result = restTemplate.postForObject("https://ucdp-apiservice-test.digiwin.com:8443/external-api/qywechat/sendMsg", requestEntity, String.class);
        JSONObject jsonObject = restTemplate.postForObject("https://ucdp-apiservice-test.digiwin.com:8443/external-api/employee/info/list/get", requestEntity, JSONObject.class);
        System.out.println("result = " + JSON.toJSONString(jsonObject));
        return BaseResponse.ok(jsonObject);
    }

}
