package com.digiwin.escloud.aioitms.report.model.serviceReport;

import com.digiwin.escloud.aioitms.es.model.EsBase;
import com.digiwin.escloud.aioitms.escloud.model.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Date 2023/7/6 14:19
 * @Created yanggld
 * @Description
 */
@ToString
@Data
@Document(indexName="t_product_service_report")
public class TProductServiceReport extends EsBase {
    /**
     * 报告名称
     */
    private String reportName;
    /**
     * 客户名
     */
    private String customerName;
    /**
     * 年度
     */
    private String year;
    /**
     * 数据开始时间
     */
    private LocalDate dataStartDate;
    /**
     * 数据结束时间
     */
    private LocalDate dataEndDate;
    /**
     * 生成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateTime;


    /**
     * 管理应用能力总结
     */
    private String managementApplicationAbilitySummary;
    /**
     * 行为应用总结
     */
    private String behaviorApplicationSummary;
    /**
     * 组织力提升总结
     */
    private String organizationalImprovementSummary;
    /**
     * 基础运维概况
     */
    private String basicOpsOverview;



    /**
     * 问题量趋势分析数据
     */
    private List<QuestionSummaryPO> questionTrendAnalysisData;
    /**
     * 问题量趋势分析结果
     */
    private String questionTrendAnalysisResult;


    /**
     * 问题归类分析数据
     */
    private List<TQuestionClassificationPO> questionModuleAnalysisData;
    /**
     * 问题归类分析结果
     */
    private String questionModuleAnalysisResult;



    /**
     * 问题类型分析数据
     */
    private List<TQuestionClassificationPO> questionClassificationData;
    /**
     * 问题类型分析结果
     */
    private String questionClassificationAnalysisResult;


    /**
     *企业
     */
    private String entName;
    private String entCode;
    /**
     * 账套数据分析
     */
    @Field(type = FieldType.Nested)
    private List<TProductServiceReportAccount> accountList;

    /**
     * 培训课程安排
     */
    private String trainingCourseArrangement;
    /**
     * 培训课程安排数据
     */
    private List<LinkedHashMap<String,Object>> trainingCourseArrangementData;

    /**
     * 年度课程培训数据
     */
    private List<Map<String, Object>> yearTrainCourseArrangementData;
    /**
     * 年度课程培训数据结果--暂未使用 用作线程计数使用
     */
    private String yearTrainCourseArrangementResult;


    /**
     * 报名情况以成效
     */
    private String registrationEffectiveness;
     /**
     * 报名情况以成效数据
     */
    private List<LinkedHashMap<String,Object>> registrationEffectivenessData;

    /**
     * 资讯类分析数据
     */
    private List<InformationQuestionPO> informationQuestionData;
    /**
     * 资讯类分析结果
     */
    private String informationQuestionAnalysisResult;


    /**
     * 建议
     */
    private String suggest;

    /**
     * 建议描述总结 - 参与线程计数
     */
    private String issueDescriptionSummaryResult;

    private String serviceCode;

    private Double reportVersion;

}
