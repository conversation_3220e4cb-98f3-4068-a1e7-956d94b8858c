package com.digiwin.escloud.aiochat.aichat.model;

import lombok.Data;

@Data
public class ChatMessage {
    private String question;
    private String sessionId;
    private String agentId;
    //问题id 因没地方查询问题id 方案调整前端传入
    private String inputId;
    //回答id 因没地方查询回答id 方案调整前端传入
    private String outputId;

    /**
     * 以下都是请求上下文参数 前端不用关注
     * 传递给后续保存使用
     */
    private Long sid; // sid
    private Long eid; // eid
    private Long userSid; // 用户Sid
    private String userId; // 用户id
    private String agentCode; // 智能体编号
    private String aiAgentId;

    // 问题对象 也是在保存时使用，不涉及前端
    private ChatMessageV2 chatMessageV2;

    private Boolean isEmptyQuestion = false;
}
