package com.digiwin.escloud.aioitms.networksecurity.model.emnus;

public enum DbType {

    Oracle("oracle", "Oracle", 0),
    SQLServer("sqlserver", "SQLServer", 1);

    private String value;
    private String msg;
    private int index;

    DbType(String value, String msg, int index) {
        this.value = value;
        this.msg = msg;
        this.index = index;
    }

    public String getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    public int getIndex() {
        return index;
    }

}
