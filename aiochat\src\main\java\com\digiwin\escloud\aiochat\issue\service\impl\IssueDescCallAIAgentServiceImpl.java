package com.digiwin.escloud.aiochat.issue.service.impl;

import com.digiwin.escloud.aiochat.aichat.ChatService;
import com.digiwin.escloud.aiochat.aichat.SseClient;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessage;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessageBuilderUtil;
import com.digiwin.escloud.aiochat.aichat.model.HeaderInfo;
import com.digiwin.escloud.aiochat.issue.dao.IssueMapper;
import com.digiwin.escloud.aiochat.issue.service.IssueDescCallAIAgentService;
import com.digiwin.escloud.aiochat.model.IssueDescRequest;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.digiwin.escloud.aiochat.aichat.utils.HtmlUtil.removeHtmlTags;

@Service
@Slf4j
public class IssueDescCallAIAgentServiceImpl implements IssueDescCallAIAgentService {
    private static final String ISSUE_DB = "escloud-db";
    private static final String ISSUE_SUMMARY_ASSISTANT = "ISSUE_SUMMARY_ASSISTANT";

    @Autowired
    private SseClient sseClient;

    @Autowired
    private IssueMapper issueMapper;

    @Autowired
    private ChatService chatService;

    @Override
    public Flux<ServerSentEvent<Object>> getIssueDescByFlow(IssueDescRequest request) {
        ChatMessage message = buildChatMessage(request);

        // 如果问题为空，返回空的流式响应
        if (message.getIsEmptyQuestion()) {
            return Flux.empty();
        }

        // 调用AI Agent服务
        return chatService.invokeIndepthAIAgent(message);
    }

    @Override
    public ResponseBase getIssueDescForReport(IssueDescRequest request) {
        ChatMessage message = buildChatMessage(request);

        if (message.getIsEmptyQuestion()) {
            return ResponseBase.ok();
        }

        Mono<ResponseBase> responseBaseMono = sseClient.callAIAgentAndAggregateAsync(message);
        return responseBaseMono.block();
    }

    /**
     * todo 案件描述比较特殊，因网络传输数据可能过大，导致占用更多的带宽资源所以在aiochat中直接查询 escloud-db的案件描述数据
     * @param request
     * @return
     */
    public ChatMessage buildChatMessage(IssueDescRequest request){
        // 设置数据库
        request.setDb(ISSUE_DB);

        // 查询问题描述列表
        List<String> descList = issueMapper.selectIssueDesc(request);

        // 拼接问题描述
        String issueDescString = "";
        if (descList != null && !descList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            int index = 1;
            for (String desc : descList) {
                if (desc != null && !desc.trim().isEmpty()) {
                    sb.append(" ").append(removeHtmlTags(desc.trim())).append("。");
                }
            }
            issueDescString = sb.toString();
        }

        // 构建HeaderInfo
        HeaderInfo headInfo = new HeaderInfo();
        headInfo.setEid(StringUtil.toString(RequestUtil.getHeaderEid()));
        headInfo.setSid(StringUtil.toString(RequestUtil.getHeaderSid()));
        headInfo.setToken(StringUtil.toString(RequestUtil.getHeaderToken()));

        // 使用工具类构建 ChatMessage
        return ChatMessageBuilderUtil.buildChatMessage(headInfo, issueDescString, ISSUE_SUMMARY_ASSISTANT);
    }


}
