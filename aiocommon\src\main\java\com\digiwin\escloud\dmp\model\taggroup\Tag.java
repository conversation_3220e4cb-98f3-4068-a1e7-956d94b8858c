package com.digiwin.escloud.dmp.model.taggroup;

import com.digiwin.escloud.dmp.model.taggroup.base.BaseData;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 9:54
 * @Description
 */
@Data
public class Tag extends BaseData {
    @ApiModelProperty("标签权限")
    private TagAuth tagAuth;
    @ApiModelProperty("标签规则类型")
    private TagRuleType ruleType;
    @ApiModelProperty("分层类型")
    private TagLayerType layerType;
    @ApiModelProperty("标签状态")
    private TagStatus tagStatus;
    private long creator;
    private String creatorName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    private Integer important;
    @ApiModelProperty("基础标签结果是否需要0值")
    private Boolean resultNeedZero;
    @ApiModelProperty("是否是权重标签")
    private Boolean weightFlag;
}
