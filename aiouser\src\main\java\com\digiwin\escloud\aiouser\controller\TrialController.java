package com.digiwin.escloud.aiouser.controller;

import com.digiwin.escloud.aiouser.model.trial.*;
import com.digiwin.escloud.aiouser.service.ITrialService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-01-05 13:51
 * @Description
 */
@Api(value = "申请试用接口", tags = {"申请试用接口"})
@RestController
@RequestMapping("/api/trial")
@Slf4j
public class TrialController extends ControllerBase {

    @Resource
    private ITrialService trialService;

    @ApiOperation(value = "保存试用申请记录")
    @PostMapping("/apply/save")
    public ResponseBase saveTrialApply(
            @ApiParam(required = true, value = "试用申请")
            @RequestBody TrialApply trialApply) {
        return ResponseBase.ok(trialService.saveTrialApply(trialApply));
    }

    @ApiOperation(value = "查询试用申请记录")
    @PostMapping("/apply/list")
    public ResponseBase getTrialApplies(
            @ApiParam(required = true, value = "请求参数")
            @RequestBody TrialApplyGetParam trialApplyGetParam) {
        return ResponseBase.ok(
                new TrialApplyGetRes(trialService.getTrialApplies(trialApplyGetParam),
                        trialService.getTrialAppliesCount(trialApplyGetParam)));
    }

    @ApiOperation(value = "开通试用")
    @PostMapping("/open")
    public BaseResponse openTrial(
            @ApiParam(required = true, value = "开通试用")
            @RequestBody TrialOpen trialOpen) {
        return trialService.openTrial(trialOpen);
    }

    @ApiOperation(value = "保存安装信息")
    @PostMapping("/install/info")
    public ResponseBase saveInstallInfo(
            @RequestBody TrialOpen trialOpen) {
        return ResponseBase.ok(trialService.saveInstallInfo(trialOpen));
    }

    @ApiOperation(value = "查询开通试用详情")
    @GetMapping("/open/detail")
    public ResponseBase getTrialOpenDetail(
            @ApiParam(required = true, value = "试用申请id")
            @RequestParam(value = "applyId") long applyId) {
        return ResponseBase.ok(trialService.getTrialOpenDetail(applyId));
    }

    @ApiOperation(value = "获取空试用模组合约列表")
    @GetMapping("/module/contract/empty/list")
    public BaseResponse getTrialModuleContractEmptyList() {
        return trialService.getTrialModuleContractEmptyList();
    }

    @ApiOperation(value = "安装成功")
    @PutMapping("/install/success")
    public ResponseBase installSuccess(
            @ApiParam(required = true, value = "试用申请id")
            @RequestParam(value = "applyId") long applyId,
            @ApiParam(required = true, value = "安装时间")
            @RequestParam(value = "installTime")
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date installTime) {
        return ResponseBase.ok(trialService.installSuccess(applyId, installTime));
    }

    @ApiOperation(value = "安装失败")
    @PutMapping("/install/fail")
    public ResponseBase installFail(
            @ApiParam(required = true, value = "试用申请id")
            @RequestParam(value = "applyId") long applyId,
            @ApiParam(required = true, value = "失败原因")
            @RequestParam(value = "failReason") String failReason) {
        return ResponseBase.ok(trialService.installFail(applyId, failReason));
    }

    @ApiOperation(value = "查询申请拒绝原因")
    @GetMapping("/apply/reject/reason")
    public ResponseBase getTrialApplyRejectReasons(
            @ApiParam(required = true, value = "试用申请id")
            @RequestParam(value = "applyId", required = true) long applyId) {
        return ResponseBase.ok(trialService.getTrialApplyRejectReasons(applyId));
    }

    @ApiOperation(value = "拒绝试用申请")
    @PostMapping("/apply/reject")
    public ResponseBase saveRejectTrialApply(
            @ApiParam(required = true, value = "拒绝原因")
            @RequestBody List<TrialApplyRejectMap> trialApplyRejectMaps) {
        return ResponseBase.ok(trialService.saveRejectTrialApply(trialApplyRejectMaps));
    }

    @ApiOperation(value = "查询拒绝原因")
    @GetMapping("/reject/reason")
    public ResponseBase getRejectReasons() {
        return ResponseBase.ok(trialService.getRejectReasons());
    }

    @ApiOperation(value = "试用及订阅到期的邮件通知")
    @PostMapping("/subscribe/expire/mail")
    public ResponseBase sendModuleContractExpireMail(
            @RequestParam(value = "specifyCheckEid", required = false, defaultValue = "0") long specifyCheckEid
    ) {
        trialService.sendModuleContractExpireMail(specifyCheckEid == 0 ? null : specifyCheckEid);
        return ResponseBase.ok();
    }

    @ApiOperation(value = "申请试用来源")
    @GetMapping("/apply/source/list")
    public ResponseBase getTrialApplySources(
            @ApiParam(required = true, value = "来源类型")
            @RequestParam(value = "sourceType", required = false) Integer sourceType,
            @ApiParam(required = true, value = "页码")
            @RequestParam(value = "page") int page,
            @ApiParam(required = true, value = "条数")
            @RequestParam(value = "size") int size) {
        return ResponseBase.ok(
                new TrialApplySourcesGetRes(trialService.getTrialApplySources(sourceType, page, size),
                        trialService.getTrialApplySourcesCount(sourceType)));
    }

    @ApiOperation(value = "获取某主题的活动列表")
    @GetMapping("/apply/active/list")
    public ResponseBase getTrialApplyActiveList(
            @ApiParam(required = true, value = "主题模型")
            @RequestParam(value = "themeModelCode", required = true) String themeModelCode) {
        return ResponseBase.ok(trialService.getTrialApplyActiveList(themeModelCode));
    }

    @ApiOperation(value = "保存活动来源")
    @PostMapping("/activity/source/save")
    public ResponseBase saveTrialApplySource(
            @ApiParam(required = true, value = "活动来源")
            @RequestBody TrialApplySource trialApplySource) {
        return trialService.saveTrialApplySource(trialApplySource);
    }
}
