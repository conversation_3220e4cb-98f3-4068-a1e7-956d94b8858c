package com.digiwin.escloud.aiouser.controller;

import com.digiwin.escloud.aiouser.model.common.BizException;
import com.digiwin.escloud.aiouser.model.tenantNotice.*;
import com.digiwin.escloud.aiouser.service.ITenantNotifyService;
import com.digiwin.escloud.aiouser.util.PageUtils;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Date 2021/6/21 11:19
 * @Created yanggld
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/tenant/notice")
@Api(tags = {"租户通知群组相关"})
public class TenantNoticeController extends ControllerBase {

    @Autowired
    private ITenantNotifyService tenantNoticeService;

    @ApiOperation(value = "列表分页查询")
    @GetMapping("/group/list")
    public ResponseBase list(@RequestParam Map<String, Object> params) {
        try {
            int pageNum = PageUtils.getPageNum(params);
            int pageSize = PageUtils.getPageSize(params);
            PageHelper.startPage(pageNum, pageSize);
            List<TenantNotifyGroupListRespDTO> tenantNotifyGroupListup = tenantNoticeService.list(params);
            PageInfo<TenantNotifyGroupListRespDTO> pageInfo = new PageInfo<>(tenantNotifyGroupListup);
            return ResponseBase.ok(pageInfo);
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "通过id查询数据")
    @GetMapping("/group/{id}")
    public ResponseBase list(@PathVariable Long id) {
        try {
            TenantNotifyGroupRespDTO tenantNotifyGroupRespDTO = tenantNoticeService.findByTenantNoticeGroupId(id);
            return ResponseBase.ok(tenantNotifyGroupRespDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "群组通知人查询")
    @GetMapping("/group")
    public ResponseBase getGroupNotifier(@RequestParam Long eid, @RequestParam(required = false) Long id) {
        return tenantNoticeService.getGroupNotifier(eid, id);
    }

    @ApiOperation(value = "保存数据")
    @PostMapping("/group/save")
    public ResponseBase save(@RequestBody TenantNotifyGroupRespDTO dto) {
        try {
            tenantNoticeService.save(dto);
            return ResponseBase.ok();
        } catch (BizException ex) {
            ex.printStackTrace();
            return ResponseBase.error(String.valueOf(ex.getCode()), ex.toString());
        }
    }

    @ApiOperation(value = "删除数据")
    @DeleteMapping("/group/{id}")
    public ResponseBase delete(@PathVariable Long id) {
        try {
            tenantNoticeService.delete(id);
            return ResponseBase.ok();
        } catch (BizException ex) {
            ex.printStackTrace();
            return ResponseBase.error(String.valueOf(ex.getCode()), ex.toString());
        }
    }

    @ApiOperation(value = "设置默认群组")
    @PostMapping("/group/default/set")
    public ResponseBase setDefault(@RequestParam("id") Long id) {
        try {
            tenantNoticeService.setDefault(id);
            return ResponseBase.ok();
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "设置群组状态")
    @PostMapping("/group/status/set")
    public ResponseBase setStatus(@RequestBody TenantNotifyGroup tenantNotifyGroup) {
        try {
            tenantNoticeService.updateTenantNotifyGroup(tenantNotifyGroup);
            return ResponseBase.ok();
        } catch (Exception ex) {
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "查询通知用户")
    @PostMapping("/group/userNoticeContact")
    public ResponseBase getUserNoticeContact(@RequestBody List<Long> tngIdList) {
        try {
            List<UserNotifyContact> userNotifyContact = tenantNoticeService.getUserNotifyContact(tngIdList);
            return ResponseBase.ok(userNotifyContact);
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }

    @ApiOperation(value = "依据客服代号获取默认通知用户")
    @GetMapping("/group/userNoticeContactByServiceCode")
    public BaseResponse getUserNoticeContactByServiceCode(
            @ApiParam(value = "客服代号或租户Sid", required = true)
            @RequestParam(value = "serviceCodeOrEid") String serviceCodeOrEid,
            @ApiParam(value = "是否是默认")
            @RequestParam(value = "isDefault", required = false, defaultValue = "true") Boolean isDefault) {
        return getBaseResponse(() -> tenantNoticeService.getUserNoticeContactByServiceCode(serviceCodeOrEid, isDefault),
                false, false, null);
    }

    @ApiOperation(value = "查询通知用户")
    @PostMapping("/group/userNoticeContactByMail")
    public ResponseBase getUserNoticeContactByMail(@RequestParam(value = "eid", required = false, defaultValue = "") String eid,
                                                   @RequestBody List<String> mailList) {
        try {
            List<UserNotifyContact> userNotifyContact = tenantNoticeService.getUserNoticeContactByMail(eid, mailList);
            return ResponseBase.ok(userNotifyContact);
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseBase.error(ex);
        }
    }


    @ApiOperation(value = "获取通知模块")
    @GetMapping("/module")
    public ResponseBase getNotifyModule() {
        return ResponseBase.ok(tenantNoticeService.getNotifyModule());
    }

    @ApiOperation(value = "获取预警级别")
    @GetMapping("/receive/level")
    public ResponseBase getNotifyReceiveLevel(@RequestParam Long nmId) {
        return ResponseBase.ok(tenantNoticeService.getNotifyReceiveLevel(nmId));
    }

    @ApiOperation(value = "获取通知方式")
    @GetMapping("/way")
    public ResponseBase getNotifyWay(@RequestParam Long nmId) {
        return ResponseBase.ok(tenantNoticeService.getNotifyWay(nmId));
    }

    @ApiOperation(value = "保存通知模块")
    @PostMapping("/module/save")
    public ResponseBase saveNotifyModule(@RequestBody UserNotifyModuleSettingReq userNotifySetting) {
        return tenantNoticeService.saveNotifyModule(userNotifySetting);
    }

    @ApiOperation(value = "获取用户是否关注公众号")
    @GetMapping("/user/wechat/subscribe")
    public ResponseBase getUserWechatSubscribe(@RequestParam String userId) {
        return tenantNoticeService.getUserWechatSubscribe(userId);
    }

    @ApiOperation(value = "获取模块通知设定")
    @GetMapping("/setting/result")
    public ResponseBase getNotifyModuleSetting(@RequestParam Long nmId, @RequestParam String authUserId, @RequestParam Long eid) {
        return tenantNoticeService.getNotifyModuleSetting(nmId, authUserId, eid);
    }


    @ApiOperation(value = "修正通知设定数据")
    @PostMapping("/fix/notify/setting")
    public ResponseBase fixNotifySetting(@RequestBody List<Long> eidList) {
        return tenantNoticeService.fixNotifySetting(eidList);
    }

    @ApiOperation(value = "清理重复的用户通知联系人数据")
    @PostMapping("/cleanup/duplicate/user/contact")
    public ResponseBase cleanupDuplicateUserNotifyContact() {
        return tenantNoticeService.cleanupDuplicateUserNotifyContact();
    }
}
