package com.digiwin.escloud.aiochat.aichat;

import com.digiwin.escloud.aiochat.aichat.model.ChatAssistant;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessage;
import com.digiwin.escloud.aiochat.aichat.model.ChatMessageFeedback;
import com.digiwin.escloud.common.model.ResponseBase;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;


public interface ChatService {

    Flux<ServerSentEvent<Object>> invokeIndepthAIAgent(ChatMessage message);

    ResponseBase saveChatMessageFeedback(ChatMessageFeedback feedback);

    ResponseBase updateChatAssistantByAiAgentId(ChatAssistant chatAssistant);

    ResponseBase insertChatAssistant(ChatAssistant chatAssistant);
}
