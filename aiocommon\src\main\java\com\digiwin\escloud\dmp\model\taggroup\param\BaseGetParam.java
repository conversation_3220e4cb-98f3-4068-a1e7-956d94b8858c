package com.digiwin.escloud.dmp.model.taggroup.param;

import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.dmp.model.taggroup.UpdateMode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date: 2022-05-06 11:41
 * @Description
 */
@Data
public class BaseGetParam {
    private Long sid;
    private Long eid;
    @ApiModelProperty("维度id")
    private long dimensionId;
    @ApiModelProperty("目录id")
    private long[] catalogueIds;
    @ApiModelProperty("应用code")
    private String appCode = RequestUtil.getHeaderAppCode();
    @ApiModelProperty("标签/分群编号或名称")
    private String content;
    @ApiModelProperty("更新方式")
    private UpdateMode updateMode;
    @ApiModelProperty(value = "页码", required = true)
    private int page;
    @ApiModelProperty(value = "条数", required = true)
    private int size;
    @ApiModelProperty("偏移量")
    private int start;

    public int getStart() {
        return (page - 1) * size;
    }
}
