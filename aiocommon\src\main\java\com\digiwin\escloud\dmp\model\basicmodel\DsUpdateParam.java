package com.digiwin.escloud.dmp.model.basicmodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date: 2023-05-30 21:42
 * @Description
 */
@Data
public class DsUpdateParam {
    private long id;
    private String tableName;
    private String statusField;
    private int updateStatus;
    private String timeField;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dataUpdateTime;
}
