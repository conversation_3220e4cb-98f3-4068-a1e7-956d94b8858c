package com.digiwin.escloud.dmp.model.taggroup;

/**
 * <AUTHOR>
 * @Date: 2022-04-22 10:25
 * @Description
 */
public enum ValueType {
    STRING,
    INT,
    DOUBLE,
    LONG,
    DATE,
    DATETIME;


    public static ValueType getByFieldType(FieldType fieldType){
        if (FieldType.INT.equals(fieldType)) {
            return ValueType.INT;
        } else if (FieldType.BIGINT.equals(fieldType)) {
            return ValueType.LONG;
        } else if (FieldType.DECIMAL.equals(fieldType)) {
            return ValueType.DOUBLE;
        } else {
            return ValueType.STRING;
        }
    }

}
