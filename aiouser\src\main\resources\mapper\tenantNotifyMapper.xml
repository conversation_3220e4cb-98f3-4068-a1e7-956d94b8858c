<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiouser.dao.ITenantNotifyDao">
    <resultMap id="tenantNotifyGroupRespDTOMap"
               type="com.digiwin.escloud.aiouser.model.tenantNotice.TenantNotifyGroupRespDTO">
        <id property="id" column="id"/>
        <result property="eid" column="eid"/>
        <result property="name" column="name"/>
        <result property="remark" column="remark"/>
        <collection property="userNotifyContactList" columnPrefix="unc_" resultMap="userNotifyContactMap" />
    </resultMap>

    <resultMap id="userNotifyContactMap"
               type="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact">
        <id property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="telephone" column="telephone"/>
        <result property="openId" column="openId"/>
        <result property="userId" column="userId"/>
        <result property="wechat" column="wechat"/>
        <result property="nwIds" column="nwIds"/>
        <result property="nrlIds" column="nrlIds"/>
        <result property="notifyWays" column="notifyWays"/>
        <result property="receiveLevels" column="receiveLevels"/>
        <result property="enabled" column="enabled"/>
    </resultMap>

    <insert id="insertTenantNoticeGroup">
        insert into tenant_notify_group(id, sid, eid, name, remark, status, dft)
        values (#{id}, #{sid}, #{eid}, #{name}, #{remark}, #{status}, #{dft})
    </insert>
    <insert id="insertUserNoticeContact">
        insert into user_notify_contact(id,sid,eid,name,email,telephone,openId,userId) values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.sid},#{item.eid},#{item.name},#{item.email},#{item.telephone},#{item.openId},#{item.userId})
        </foreach>
        ON DUPLICATE KEY UPDATE sid = VALUES(sid), eid = VALUES(eid), name = VALUES(name), email = VALUES(email),
        telephone = VALUES(telephone), openId = VALUES(openId),userId = VALUES(userId)
    </insert>
    <insert id="insertTenantNoticeGroupUserMapping">
        insert into tenant_notify_group_user_mapping(tngId,uncId) values
        <foreach collection="list" item="item" separator=",">
            (#{tenantNoticeGroupId},#{item})
        </foreach>
    </insert>
    <update id="updateTenantNoticeGroup">
        update tenant_notify_group
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="remark != null">
                remark=#{remark},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="dft != null">
                dft=#{dft},
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteUserNoticeContactAndMapping">
        delete m
        from user_notify_contact u inner join tenant_notify_group_user_mapping m
        where u.id = m.uncId and m.tngId = #{tenantNoticeGroupId}
    </delete>
    <delete id="deleteAll">
        delete m,t
        from tenant_notify_group t
        inner join tenant_notify_group_user_mapping m on t.id = m.tngId
        where t.id = #{tenantNoticeGroupId}
    </delete>
    <select id="list" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.TenantNotifyGroupListRespDTO">
        select  t.*,GROUP_CONCAT(u.name separator ";") as userNotifyContacts
        from tenant_notify_group t
        left join tenant_notify_group_user_mapping m on t.id = m.tngId
        left join user_notify_contact u on u.id = m.uncId
        <where>
            <if test="sid != null">
                and t.sid= #{sid}
            </if>
            <if test="eid != null">
                and t.eid= #{eid}
            </if>
            <if test="dft != null">
                and t.dft= #{dft}
            </if>
        </where>
        group by t.id
        ORDER BY dft desc,`status` desc,t.id desc
    </select>
    <select id="findById" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.TenantNotifyGroup">
        select *
        from tenant_notify_group
        where id = #{id}
    </select>
    <select id="findByTenantNoticeGroupId"
            resultType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact">
        SELECT u.*, s.wechat, unsr.nwIds, unsr.nrlIds
        FROM user_notify_contact u
                 INNER JOIN tenant_notify_group_user_mapping m ON u.id = m.uncId
                 INNER JOIN tenant_notify_group t ON t.id = m.tngId
                 LEFT JOIN USER s ON u.userId = s.id
                 LEFT JOIN user_notify_setting_result unsr on u.userId = unsr.userId
        WHERE t.id = #{tenantNoticeGroupId}
    </select>
    <select id="getUserNotifyContact"
            resultType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact">
        select distinct t.eid,u.name,u.email,u.telephone,u.openId,s.wechat,unsr.nwIds,unsr.nrlIds,
            CASE
            WHEN (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
            FROM notify_way nw
            WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0) IS NULL
            OR (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
            FROM notify_way nw
            WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0) = ''
            THEN
            CASE
            WHEN u.email IS NOT NULL AND u.email != '' THEN 'MAIL'
            WHEN u.telephone IS NOT NULL AND u.telephone != '' THEN 'SMS'
            ELSE NULL
            END
            ELSE (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
            FROM notify_way nw
            WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0)
            END AS notifyWays,
        (SELECT GROUP_CONCAT( nrl.CODE ORDER BY nrl.id SEPARATOR ',' )
        FROM notify_receive_level nrl WHERE FIND_IN_SET( nrl.id, unsr.nrlIds ) > 0) AS receiveLevels,
        if(u.userId  IS NULL,true,unms.enabled )AS enabled
        from user_notify_contact u
        inner join tenant_notify_group t
        inner join tenant_notify_group_user_mapping m
        LEFT JOIN USER s ON u.userId = s.id
        LEFT JOIN user_notify_setting_result unsr ON u.userId = unsr.userId
        LEFT JOIN user_notify_module_setting unms ON s.sid = unms.userSid
        where u.id = m.uncId
        and t.id = m.tngId AND t.status = 1
        and t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectUserNoticeContactByMap" resultMap="tenantNotifyGroupRespDTOMap">
        select distinct tng.id, tng.eid, tng.name, tng.remark,
                        unc.name as unc_name, unc.email as unc_email, unc.telephone as unc_telephone,
                        unc.openId as unc_openId,
                        s.wechat AS unc_wechat,
                        unsr.nwIds AS unc_nwIds,
                        unsr.nrlIds AS unc_nrlIds,
                        CASE 
                            WHEN (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
                                 FROM notify_way nw
                                 WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0) IS NULL 
                                 OR (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
                                    FROM notify_way nw
                                    WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0) = '' 
                            THEN 
                                CASE 
                                    WHEN unc.email IS NOT NULL AND unc.email != '' THEN 'MAIL'
                                    WHEN unc.telephone IS NOT NULL AND unc.telephone != '' THEN 'SMS'
                                    ELSE NULL
                                END
                            ELSE (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
                                 FROM notify_way nw
                                 WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0)
                        END AS unc_notifyWays,
                        (SELECT GROUP_CONCAT( nrl.code ORDER BY nrl.id SEPARATOR ',' )
                        FROM notify_receive_level nrl WHERE FIND_IN_SET( nrl.id, unsr.nrlIds ) > 0) AS unc_receiveLevels,
        if(unc.userId  IS NULL,true,unms.enabled )AS unc_enabled
        from user_notify_contact unc
        inner join tenant_notify_group_user_mapping tngum on unc.id = tngum.uncId
        inner join tenant_notify_group tng on tngum.tngId = tng.id
        LEFT JOIN USER s ON unc.userId = s.id
        LEFT JOIN user_notify_setting_result unsr on unc.userId = unsr.userId
        LEFT JOIN user_notify_module_setting unms ON s.sid = unms.userSid
        <if test="serviceCodeOrEid != null and serviceCodeOrEid != ''">
            inner join supplier_tenant_map stm on tng.eid = stm.eid
                and (stm.serviceCode = #{serviceCodeOrEid} or stm.eid = ${serviceCodeOrEid})
        </if>
        <where>
            <if test="isDefault != null">
                and tng.dft = #{isDefault}
            </if>
            <if test="status != null">
                and tng.status = #{status}
            </if>
        </where>
    </select>
    <select id="getUserNoticeContactByMail"
            resultType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact">
        select distinct t.eid, u.name,u.email,u.telephone,u.openId,s.wechat AS wechat,unsr.nwIds AS nwIds,unsr.nrlIds AS nrlIds,
            CASE
            WHEN (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
            FROM notify_way nw
            WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0) IS NULL
            OR (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
            FROM notify_way nw
            WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0) = ''
            THEN
            CASE
            WHEN u.email IS NOT NULL AND u.email != '' THEN 'MAIL'
            WHEN u.telephone IS NOT NULL AND u.telephone != '' THEN 'SMS'
            ELSE NULL
            END
            ELSE (SELECT GROUP_CONCAT( nw.code ORDER BY nw.id SEPARATOR ',' )
            FROM notify_way nw
            WHERE FIND_IN_SET( nw.id, unsr.nwIds ) > 0)
            END AS notifyWays,
        (SELECT GROUP_CONCAT( nrl.code ORDER BY nrl.id SEPARATOR ',' )
        FROM notify_receive_level nrl WHERE FIND_IN_SET( nrl.id, unsr.nrlIds ) > 0) AS receiveLevels,
        if(u.userId  IS NULL,true,unms.enabled )AS enabled
        from user_notify_contact u
             inner join tenant_notify_group t
             inner join tenant_notify_group_user_mapping m
             LEFT JOIN USER s ON u.userId = s.id
             LEFT JOIN user_notify_setting_result unsr on u.userId = unsr.userId
             LEFT JOIN user_notify_module_setting unms ON s.sid = unms.userSid
        where u.id = m.uncId
          and t.id = m.tngId
          and u.email in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="eid != null and eid != ''">
            and t.eid = #{eid}
        </if>
    </select>

    <select id="getNotifyModule" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.NotifyModule">
        select id as nmId,
               code as moduleCode,
               name as moduleName
        from notify_module
    </select>

    <select id="getNotifyReceiveLevel" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.NotifyReceiveLevel">
        SELECT
            nrl.id,
            nrl.CODE AS receiveLevelCode,
            nrl.NAME AS receiveLevelName
        FROM
            notify_module_setting nms
                JOIN notify_receive_level nrl ON FIND_IN_SET( nrl.id, nms.nrlIds ) > 0
        WHERE nms.nmId = #{nmId}
    </select>


    <select id="getNotifyWay" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.NotifyWay">
        SELECT
            nw.id,
            nw.CODE AS wayCode,
            nw.NAME AS wayName
        FROM
            notify_module_setting nms
                JOIN notify_way nw ON FIND_IN_SET( nw.id, nms.nwIds ) > 0
        WHERE nms.nmId = #{nmId}
    </select>


    <insert id="insertOrUpdateUserNotifyModuleSetting" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyModuleSetting">
        INSERT INTO user_notify_module_setting (id, userSid, nmId, enabled, createTime, updateTime)
            VALUES ( #{id}, #{userSid}, #{nmId}, #{enabled}, #{createTime}, #{updateTime} )
        ON DUPLICATE KEY UPDATE
            enabled = VALUES(enabled),
            updateTime = VALUES(updateTime)
    </insert>

    <insert id="batchInsertOrUpdateUserNotifyModuleSetting" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyModuleSetting">
        INSERT INTO user_notify_module_setting (id, userSid, nmId, enabled, createTime, updateTime)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.userSid}, #{item.nmId}, #{item.enabled}, #{item.createTime},#{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        enabled = VALUES(enabled),
        updateTime = VALUES(updateTime)
    </insert>

    <update id="updateUserNotifyContact" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact">
        update user_notify_contact unc
        JOIN (
        SELECT userId, MAX(id) AS max_id
        FROM user_notify_contact
        WHERE userId = #{userId}
        ) AS tmp ON unc.userId = tmp.userId AND unc.id = tmp.max_id
        set unc.email = #{email},
            unc.telephone = #{telephone}
        where unc.userId = #{userId}
    </update>

    <insert id="insertOrUpdateUserNotifySettingResult" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResult">
        INSERT INTO user_notify_setting_result (id, userSid, userId, nmId, nwIds, nrlIds, createTime, updateTime)
            VALUES ( #{id}, #{userSid}, #{userId}, #{nmId}, #{nwIds}, #{nrlIds}, #{createTime}, #{updateTime})
        ON DUPLICATE KEY UPDATE
                             nwIds = VALUES(nwIds),
                             nrlIds = VALUES(nrlIds),
                             updateTime = VALUES(updateTime)
    </insert>

    <insert id="batchInsertOrUpdateUserNotifySettingResult" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResult">
        INSERT INTO user_notify_setting_result (id, userSid, userId, nmId, nwIds, nrlIds, createTime, updateTime)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.userSid}, #{item.userId}, #{item.nmId},#{item.nwIds}, #{item.nrlIds},
             #{item.createTime}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        nwIds = VALUES(nwIds),
        nrlIds = VALUES(nrlIds),
        updateTime = VALUES(updateTime)
    </insert>

    <select id="getSettingResultRes" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResultRes">
        SELECT
            unc.telephone,
            unc.email,
            u.wechat,
            unms.enabled,
            unsr.*
        FROM
            USER u
                LEFT JOIN user_notify_setting_result unsr ON u.id = unsr.userId
                AND unsr.nmId = #{nmId}
                LEFT JOIN user_notify_module_setting unms ON unsr.userSid = unms.userSid
                AND unms.nmId = #{nmId}
                LEFT JOIN user_notify_contact unc ON unc.userId = u.id AND unc.id = (
                SELECT MAX(id)
                FROM user_notify_contact
                WHERE userId = u.id
            )
        WHERE
            u.id = #{userId}
            LIMIT 1
    </select>

    <select id="getUserNotifySettingResultByUserModule" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResultRes">
        SELECT unms.enabled,unsr.*,
               (SELECT GROUP_CONCAT( nrl.CODE ORDER BY nrl.id SEPARATOR ',' )
                FROM notify_receive_level nrl WHERE FIND_IN_SET( nrl.id, unsr.nrlIds ) > 0) AS receiveLevels
        FROM user_notify_setting_result unsr
        LEFT JOIN notify_module nm ON nm.id = unsr.nmId
        LEFT JOIN user_notify_module_setting unms ON unsr.userSid = unms.userSid AND unms.nmId = nm.id
        WHERE nm.`code`=#{nmCode} and unsr.userId IN
        <foreach collection="userIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getUserByEid" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.NotifyContactResp">
        SELECT
            u.sid,
            u.id,
            u.wechat,
            u.openId,
            unc.id as uncId,
            unc.name,
            unc.telephone,
            unc.email,
            unsr.nwIds,
            unsr.nrlIds
        FROM
            user_notify_contact unc
                LEFT JOIN USER u ON unc.userId = u.id
                LEFT JOIN user_tenant_map utm ON u.sid = utm.userSid
                LEFT JOIN user_notify_setting_result unsr ON u.id = unsr.userId
                AND unsr.nmId = 1
        WHERE
            utm.eid = #{eid}
        GROUP BY
            u.sid
    </select>

    <select id="selectNotifyContactByEid" resultType="com.digiwin.escloud.aiouser.model.tenantNotice.FixNotifyUser">
        SELECT
            u.*, utm.eid
        FROM
            USER u
            INNER JOIN user_tenant_map utm ON u.sid = utm.userSid
            INNER JOIN user_notify_contact unc ON u.id = unc.userId
        <where>
            <if test="eidList != null and eidList.size() > 0">
                utm.eid IN
                <foreach collection="eidList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY u.sid
    </select>

    <select id="getUserByUserId" resultType="com.digiwin.escloud.aiouser.model.user.User">
        select * from user where id = #{userId}
    </select>

    <select id="selectUserNoticeContactByUqKey" resultType="int">
        select count(1) from user_notify_contact where userId = #{userId}
    </select>

    <select id="selectUserNoticeContactByUser" resultType="int">
        select count(1) from user_notify_contact where userId = #{userId}
    </select>

    <select id="selectModuleSettingByUserModule" resultType="int">
        select count(1) from user_notify_module_setting where userSid = #{userSid} and nmId = #{nmId}
    </select>

    <select id="selectUserNoticeSettingResultByUserModule" resultType="int">
        select count(1) from user_notify_setting_result where userId = #{userId} and nmId = #{nmId}
    </select>

    <insert id="insertUserNotifySettingResult" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResult">
        INSERT INTO user_notify_setting_result (id, userSid, userId, nmId, nwIds, nrlIds, createTime, updateTime)
        VALUES ( #{id}, #{userSid}, #{userId}, #{nmId}, #{nwIds}, #{nrlIds}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertUserNotifyModuleSetting" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyModuleSetting">
        INSERT INTO user_notify_module_setting (id, userSid, nmId, enabled, createTime, updateTime)
        VALUES ( #{id}, #{userSid}, #{nmId}, #{enabled}, #{createTime}, #{updateTime} )
    </insert>

    <insert id="insertUserNotifyContact" parameterType="com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifyContact">
        insert into user_notify_contact(id,sid,eid,name,email,telephone,openId,userId)
        values (#{id},#{sid},#{eid},#{name},#{email},#{telephone},#{openId},#{userId})
    </insert>

    <!-- 查找user_notify_contact表中userId重复的数据，返回需要删除的记录ID列表 -->
    <select id="findDuplicateUserNotifyContactIds" resultType="long">
        SELECT unc.id
        FROM user_notify_contact unc
        INNER JOIN (
            SELECT userId, MAX(id) as maxId
            FROM user_notify_contact
            WHERE userId IS NOT NULL AND userId != ''
            GROUP BY userId
            HAVING COUNT(*) > 1
        ) max_records ON unc.userId = max_records.userId
        WHERE unc.userId IS NOT NULL AND unc.userId != ''
        AND unc.id != max_records.maxId
    </select>

    <!-- 查找需要更新的映射关系 -->
    <select id="findMappingUpdateInfo" resultType="map">
        SELECT
            tngum.tngId,
            tngum.uncId as oldUncId,
            max_records.maxId as newUncId
        FROM tenant_notify_group_user_mapping tngum
        INNER JOIN user_notify_contact unc ON tngum.uncId = unc.id
        INNER JOIN (
            SELECT userId, MAX(id) as maxId
            FROM user_notify_contact
            WHERE userId IS NOT NULL AND userId != ''
            GROUP BY userId
            HAVING COUNT(*) > 1
        ) max_records ON unc.userId = max_records.userId
        WHERE tngum.uncId IN
        <foreach collection="duplicateIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 批量更新tenant_notify_group_user_mapping表的uncId -->
    <update id="batchUpdateTenantNotifyGroupUserMapping">
        <foreach collection="updateInfoList" item="item" separator=";">
            UPDATE tenant_notify_group_user_mapping
            SET uncId = #{item.newUncId}
            WHERE tngId = #{item.tngId} AND uncId = #{item.oldUncId}
        </foreach>
    </update>

    <!-- 批量删除user_notify_contact表中的重复记录 -->
    <delete id="batchDeleteDuplicateUserNotifyContact">
        DELETE FROM user_notify_contact
        WHERE id IN
        <foreach collection="duplicateIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>