package com.digiwin.escloud.dmp.model.basicmodel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DimensionResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 运营商sid
     */
    private Long sid;

    /**
     * 租户sid
     */
    private Long eid;

    /**
     * 应用code
     */
    private String appCode;

    /**
     * 维度编号
     */
    private String code;

    /**
     * 维度名称
     */
    private String name;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
