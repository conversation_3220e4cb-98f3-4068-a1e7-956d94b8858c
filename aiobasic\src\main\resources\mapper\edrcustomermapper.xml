<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.edr.dao.EdrCustomerMapper">
    <resultMap id="customerorgMap" type="com.digiwin.escloud.aiobasic.edr.model.edr.CustomerOrgMap">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="customerName" column="customerName"/>
        <result property="customerFullName" column="customerFullName"/>
        <result property="serverId" column="serverId"/>
        <result property="orgId" column="orgId"/>
        <result property="location" column="location"/>
        <result property="handleTime" column="handleTime"/>
        <association property="serverConf" columnPrefix="sc_" resultMap="serverConfMap"/>
        <association property="org" columnPrefix="org_" resultMap="orgMap"/>
    </resultMap>
    <resultMap id="serverConfMap" type="com.digiwin.escloud.aiobasic.edr.model.edr.ServerConf">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="sid" column="sid"/>
        <result property="name" column="name"/>
        <result property="uri" column="uri"/>
        <result property="userName" column="userName"/>
        <result property="passWord" column="passWord"/>
    </resultMap>
    <resultMap id="orgMap" type="com.digiwin.escloud.aiobasic.edr.model.edr.Org">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serverId" column="serverId"/>
        <result property="orgEdrId" column="orgEdrId"/>
        <result property="organizationId" column="organizationId"/>
        <result property="name" column="name"/>
        <result property="workstationsAllocated" column="workstationsAllocated"/>
        <result property="serversAllocated" column="serversAllocated"/>
        <result property="iotAllocated" column="iotAllocated"/>
        <result property="workstationsInUse" column="workstationsInUse"/>
        <result property="serversInUse" column="serversInUse"/>
        <result property="iotInUse" column="iotInUse"/>
        <result property="workstationsNotInUse" column="workstationsNotInUse"/>
        <result property="serversNotInUse" column="serversNotInUse"/>
        <result property="iotNotInUse" column="iotNotInUse"/>
        <result property="expirationDate" column="expirationDate"/>
        <result property="expired" column="expired"/>
        <result property="expiringSoon" column="expiringSoon"/>
        <result property="enableRenewal" column="enableRenewal"/>
        <result property="marketUrl" column="marketUrl"/>

        <result property="syncStatus" column="syncStatus"/>
        <result property="voidReason" column="voidReason"/>
        <result property="voidTime" column="voidTime"/>
        <result property="processUserId" column="processUserId"/>
        <result property="processUserName" column="processUserName"/>
    </resultMap>

    <select id="getCustomerOrgs" resultMap="customerorgMap">
        SELECT a.id,a.sid,a.serviceCode,a.customerName,a.customerFullName,a.serverId,a.orgId,a.location,a.handleTime,
        b.id as sc_id,b.code as sc_code,b.sid as sc_sid,b.name as sc_name,b.uri as sc_uri,b.userName as sc_userName,b.passWord as sc_passWord,
        c.id as org_id,c.sid as org_sid,c.serverId as org_serverId,c.orgEdrId as org_orgEdrId,c.orgEdrId as org_organizationId,c.name as org_name,
        c.workstationsAllocated as org_workstationsAllocated,c.serversAllocated as org_serversAllocated,c.iotAllocated as org_iotAllocated,
        c.workstationsInUse as org_workstationsInUse,c.serversInUse as org_serversInUse,c.iotInUse as org_iotInUse,c.expirationDate as org_expirationDate,
        c.syncStatus as org_syncStatus,c.voidReason as org_voidReason,c.voidTime as org_voidTime,c.processUserId as org_processUserId,c.processUserName as org_processUserName,
        CASE WHEN DATEDIFF(NOW(),c.expirationDate)<![CDATA[>]]>0 then 1 ELSE 0 END as org_expired
        FROM edr_customer_org_map a
        left join edr_server_conf b on a.serverId=b.id
        left join edr_org c on a.orgId=c.id
        WHERE 1=1
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        order by a.id desc
        <if test="size>0">
            LIMIT #{start} , #{size}
        </if>
    </select>

    <select id="getCustomerOrgCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM edr_customer_org_map
        WHERE sid = #{sid} and serviceCode= #{serviceCode}
    </select>

    <select id="getOrg" resultType="java.lang.Long">
        select id
        from edr_org
        where sid = #{sid} and serverId= #{serverId} and orgEdrId= #{orgEdrId}
        limit 1
    </select>

    <select id="getOrgByServerIdOrganization" resultType="java.lang.Long">
        select id
        from edr_org
        where sid = #{sid} and serverId= #{serverId} and name= #{organization}
            limit 1
    </select>


    <insert id="insertOrg" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.Org">
        insert into edr_org(id,sid,serverId,orgEdrId,name,workstationsAllocated,serversAllocated,iotAllocated,workstationsInUse,
        serversInUse,iotInUse,expirationDate,vulnerabilityAndIoT,forensicsAndEDR,verificationCode,isAdminAccount)
        values(#{id}, #{sid}, #{serverId}, #{orgEdrId}, #{name}, #{workstationsAllocated}, #{serversAllocated}, #{iotAllocated}, #{workstationsInUse},
        #{serversInUse}, #{iotInUse}, #{expirationDate}, #{vulnerabilityAndIoT}, #{forensicsAndEDR}, #{verificationCode}, #{isAdminAccount})
    </insert>

    <update id="updateOrg">
        update edr_org
        set name= #{name},workstationsAllocated= #{workstationsAllocated},serversAllocated= #{serversAllocated},iotAllocated= #{iotAllocated},
        workstationsInUse= #{workstationsInUse},serversInUse= #{serversInUse},iotInUse= #{iotInUse},expirationDate= #{expirationDate},
        vulnerabilityAndIoT= #{vulnerabilityAndIoT},forensicsAndEDR= #{forensicsAndEDR},verificationCode= #{verificationCode},isAdminAccount= #{isAdminAccount}
        where id = #{id}
    </update>

    <insert id="saveCustomerOrgMap" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.CustomerOrgMap">
        insert into edr_customer_org_map(id,sid,serviceCode,customerName,customerFullName,serverId,orgId,location)
        values(#{id}, #{sid}, #{serviceCode}, #{customerName},  #{customerFullName}, #{serverId}, #{orgId}, #{location})
        ON DUPLICATE KEY UPDATE serverId=#{serverId},orgId=#{orgId},location=#{location},customerFullName=#{customerFullName}
    </insert>

    <delete id="deleteCustomerOrgMap">
        DELETE FROM edr_customer_org_map
        WHERE id = #{id}
    </delete>

    <select id="getCustomerLocations" resultType="java.lang.String">
        SELECT distinct(location)
        FROM edr_customer_org_map
        WHERE sid = #{sid} and serviceCode= #{serviceCode}
    </select>

    <select id="getOrgDetail" resultType="com.digiwin.escloud.aiobasic.edr.model.edr.Org">
        select id,sid,serverId,orgEdrId,name,workstationsAllocated,serversAllocated,iotAllocated,workstationsInUse,
        serversInUse,iotInUse,expirationDate,vulnerabilityAndIoT,forensicsAndEDR,verificationCode,isAdminAccount
        from edr_org
        where id = #{id}
    </select>

    <update id="updateCustomerOrgSync">
        update edr_customer_org_map
        set handleTime= #{handleTime}
        where id = #{id}
    </update>

    <select id="getOrgs" resultMap="customerorgMap">
        SELECT a.id,a.sid,a.serviceCode,a.customerName,a.serverId,a.orgId,a.location,a.handleTime,
        b.id as sc_id,b.code as sc_code,b.sid as sc_sid,b.name as sc_name,b.uri as sc_uri,b.userName as sc_userName,b.passWord as sc_passWord,
        c.id as org_id,c.sid as org_sid,c.serverId as org_serverId,c.orgEdrId as org_orgEdrId,c.orgEdrId as org_organizationId,c.name as org_name,
        c.workstationsAllocated as org_workstationsAllocated,c.serversAllocated as org_serversAllocated,c.iotAllocated as org_iotAllocated,
        c.workstationsInUse as org_workstationsInUse,c.serversInUse as org_serversInUse,c.iotInUse as org_iotInUse,
        c.expirationDate as org_expirationDate,
        CASE WHEN DATEDIFF(c.expirationDate, CURDATE()) BETWEEN 0 AND 60 THEN true ELSE false END as org_expiringSoon, /*即将到期*/
        ${marketUrl} as org_marketUrl, /*雲端市場單一登入url*/
        (select enableRenewal from supplier_aiops_module where moduleCode = 'EDR') as org_enableRenewal, /*是否開啟續費*/
        c.workstationsAllocated-c.workstationsInUse as org_workstationsNotInUse,c.serversAllocated-c.serversInUse as org_serversNotInUse,
        c.iotAllocated-c.iotInUse as org_iotNotInUse,
        c.syncStatus as org_syncStatus,c.voidReason as org_voidReason,c.voidTime as org_voidTime,c.processUserId as org_processUserId,c.processUserName as org_processUserName
        FROM edr_customer_org_map a
        left join edr_server_conf b on a.serverId=b.id
        left join edr_org c on a.orgId=c.id
        WHERE 1=1
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="locations != null and locations.length > 0">
            and a.location in
            <foreach collection="locations" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orgIds != null and orgIds.length > 0">
            and a.orgId in
            <foreach collection="orgIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by c.expirationDate
    </select>

    <select id="getTenantSupplement" resultType="com.digiwin.escloud.aiobasic.edr.model.base.TenantSupplement">
        SELECT id,sid,serviceCode,remarks
        from edr_tenant_supplement
        where serviceCode= #{serviceCode} and sid=#{sid}
    </select>

    <insert id="saveCustomerRemarks" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.TenantSupplement">
        insert into edr_tenant_supplement(id,sid,serviceCode,remarks)
        values(#{id}, #{sid}, #{serviceCode}, #{remarks})
        ON DUPLICATE KEY UPDATE sid=#{sid},remarks=#{remarks}
    </insert>

    <select id="getCustomerOrgExist" resultType="java.lang.Integer">
        select 1
        from edr_customer_org_map
        where serviceCode= #{serviceCode} and sid = #{sid}
        <if test="serverId>0">
            and serverId = #{serverId}
        </if>
        <if test="orgId>0">
            and orgId = #{orgId}
        </if>
        <if test="location!=null and location!=''">
            and location= #{location}
        </if>
        <if test="mode == 'update' ">
            and id!=#{id}
        </if>
        limit 1
    </select>

    <select id="getCustomerOrgById" resultMap="customerorgMap">
        select id,sid,serviceCode,customerName,serverId,orgId,location,handleTime
        from edr_customer_org_map
        where id=#{id}
        limit 1
    </select>

    <select id="getCustomerOrgByServiceCodeAndOrganization" resultMap="customerorgMap">
        SELECT a.id,a.sid,a.serviceCode,a.customerName,a.serverId,a.orgId,a.location,a.handleTime,
        b.id as sc_id,b.code as sc_code,b.sid as sc_sid,b.name as sc_name,b.uri as sc_uri,b.userName as sc_userName,b.passWord as sc_passWord,
        c.id as org_id,c.sid as org_sid,c.serverId as org_serverId,c.orgEdrId as org_orgEdrId,c.orgEdrId as org_organizationId,c.name as org_name,
        c.workstationsAllocated as org_workstationsAllocated,c.serversAllocated as org_serversAllocated,c.iotAllocated as org_iotAllocated,
        c.workstationsInUse as org_workstationsInUse,c.serversInUse as org_serversInUse,c.iotInUse as org_iotInUse,c.expirationDate as org_expirationDate,
        CASE WHEN DATEDIFF(NOW(),c.expirationDate)<![CDATA[>]]>0 then 1 ELSE 0 END as org_expired
        FROM edr_customer_org_map a
        left join edr_server_conf b on a.serverId=b.id
        left join edr_org c on a.orgId=c.id and a.serverId=c.serverId
        WHERE 1=1
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="organization!=null and organization!=''">
            and c.name= #{organization}
        </if>
        <if test="serverId>0">
            and a.serverId= #{serverId}
        </if>
        limit 1
    </select>

    <update id="saveCustomerInvalid" parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCustomerInvalidParam">
        UPDATE edr_org SET
        <if test="processUserId != null">
            processUserId = #{processUserId},
        </if>
        <if test="processUserName != null">
            processUserName = #{processUserName},
        </if>
        <if test="voidReason != null">
            voidReason = #{voidReason}, voidTime = #{voidTime},
        </if>
        syncStatus = #{syncStatus}
        WHERE id = #{orgId}
    </update>
    <select id="checkOrgVoid" resultType="java.lang.Integer">
        select syncStatus
        from edr_org
        where id=#{orgId}
    </select>
    <select id="getCustomerNameByOrgIds" parameterType="java.util.List" resultType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrReportOrgCnNameList">
        select orgId, customerFullName,location
        from edr_customer_org_map
        where orgId in
        <foreach collection="orgIds" item="orgId" open="(" close=")" separator=",">
            #{orgId}
        </foreach>
        and serviceCode = #{serviceCode}
    </select>

    <update id="cancelCustomerInvalid" parameterType="java.lang.String">
        UPDATE edr_org
        SET syncStatus = 1
        WHERE name IN (
            <foreach collection="orgNameList" item="orgName" separator=",">
                #{orgName}
            </foreach>
            )
    </update>
</mapper>