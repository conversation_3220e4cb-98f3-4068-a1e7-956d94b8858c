package com.digiwin.escloud.aiouser.dao;

import com.digiwin.escloud.aiouser.model.tenantNotice.*;
import com.digiwin.escloud.aiouser.model.user.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Date 2021/6/21 14:22
 * @Created yanggld
 * @Description
 */
@Mapper
public interface ITenantNotifyDao {

    List<TenantNotifyGroupListRespDTO> list(Map<String, Object> params);

    TenantNotifyGroup findById(Long id);

    List<UserNotifyContact> findByTenantNoticeGroupId(Long id);

    int insertTenantNoticeGroup(TenantNotifyGroup tenantNotifyGroup);

    int insertUserNoticeContact(List<UserNotifyContact> userNotifyContactList);

    int updateTenantNoticeGroup(TenantNotifyGroup tenantNotifyGroup);

    void deleteUserNoticeContactAndMapping(Long tenantNoticeGroupId);

    int insertTenantNoticeGroupUserMapping(@Param("tenantNoticeGroupId")Long tenantNoticeGroupId,@Param("list") List<Long> userNoticeContactIdList);

    int deleteAll(Long tenantNoticeGroupId);

    List<UserNotifyContact> getUserNotifyContact(List<Long> tngIdList);

    /**
     * 依据条件字典查询通知用户
     * @param map 条件字典
     * @return 通知用户列表
     */
    List<TenantNotifyGroupRespDTO> selectUserNoticeContactByMap(Map<String, Object> map);

    List<UserNotifyContact> getUserNoticeContactByMail(@Param("eid")String eid,
                                                       @Param("list") List<String> mailList);

    /**
     * 获取通知模块
     *
     * @return
     */
    List<NotifyModule> getNotifyModule();

    /**
     * 获取预警接收级别
     *
     * @param nmId
     * @return
     */
    List<NotifyReceiveLevel> getNotifyReceiveLevel(Long nmId);

    /**
     * 获取通知方式
     *
     * @param nmId
     * @return
     */
    List<NotifyWay> getNotifyWay(Long nmId);

    /**
     * 新增或更新模型通知设定
     *
     * @param setting
     * @return
     */
    Integer insertOrUpdateUserNotifyModuleSetting(UserNotifyModuleSetting setting);

    /**
     * 批量新增或更新模型通知设定
     *
     * @param settingList
     * @return
     */
    Integer batchInsertOrUpdateUserNotifyModuleSetting(List<UserNotifyModuleSetting> settingList);

    /**
     * 更新用户信息
     *
     * @param userNotifyContact
     * @return
     */
    Integer updateUserNotifyContact(UserNotifyContact userNotifyContact);

    /**
     * 新增或者更新设定结果
     *
     * @param settingResult
     * @return
     */
    Integer insertOrUpdateUserNotifySettingResult(UserNotifySettingResult settingResult);

    /**
     * 批量新增或者更新设定结果
     *
     * @param resultList
     * @return
     */
    Integer batchInsertOrUpdateUserNotifySettingResult(List<UserNotifySettingResult> resultList);


    /**
     * 获取设定结果
     *
     * @param userId
     * @param nmId
     * @return
     */
    UserNotifySettingResultRes getSettingResultRes(@Param("userId") String userId,
                                                   @Param("nmId") Long nmId);

    List<UserNotifySettingResultRes> getUserNotifySettingResultByUserModule(@Param("nmCode") String nmCode,
                                                                            @Param("userIdList") List<String> userIdList);

    /**
     *
     * @param eid
     * @return
     */
    List<NotifyContactResp> getUserByEid(Long eid);

    /**
     * 获取通知联系人
     *
     * @param eidList
     * @return
     */
    List<FixNotifyUser> selectNotifyContactByEid(@Param("eidList") List<Long> eidList);

    /**
     * 获取用户对象
     *
     * @param userId
     * @return
     */
    User getUserByUserId(String userId);

    int selectUserNoticeContactByUqKey(UserNotifyContact userNotifyContact);

    int selectUserNoticeContactByUser(String userId);

    int selectModuleSettingByUserModule(@Param("userSid") Long userSid,
                                        @Param("nmId") Long nmId);

    int selectUserNoticeSettingResultByUserModule(@Param("userId") String userId,
                                                  @Param("nmId") Long nmId);

    int insertUserNotifySettingResult(UserNotifySettingResult userNotifySettingResult);

    int insertUserNotifyModuleSetting(UserNotifyModuleSetting userNotifyModuleSetting);

    int insertUserNotifyContact(UserNotifyContact userNotifyContact);

    /**
     * 查找user_notify_contact表中userId重复的数据，返回需要删除的记录ID列表
     * @return 需要删除的记录ID列表
     */
    List<Long> findDuplicateUserNotifyContactIds();

    /**
     * 查找需要更新的映射关系
     * @param duplicateIds 重复的ID列表
     * @return 映射关系更新信息
     */
    List<Map<String, Object>> findMappingUpdateInfo(@Param("duplicateIds") List<Long> duplicateIds);

    /**
     * 批量更新tenant_notify_group_user_mapping表的uncId
     * @param updateInfoList 更新信息列表
     * @return 更新的记录数
     */
    int batchUpdateTenantNotifyGroupUserMapping(@Param("updateInfoList") List<Map<String, Object>> updateInfoList);

    /**
     * 批量删除user_notify_contact表中的重复记录
     * @param duplicateIds 需要删除的ID列表
     * @return 删除的记录数
     */
    int batchDeleteDuplicateUserNotifyContact(@Param("duplicateIds") List<Long> duplicateIds);
}
