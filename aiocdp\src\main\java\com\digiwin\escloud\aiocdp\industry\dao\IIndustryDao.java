package com.digiwin.escloud.aiocdp.industry.dao;

import com.digiwin.escloud.aiocdp.industry.model.CdpLog;
import com.digiwin.escloud.aiocdp.industry.model.Staff;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2025-05-28 18:06
 * @Description
 */
public interface IIndustryDao {

    int saveCdpLog(@Param("industryDBName") String industryDBName,
                   @Param("cdpLogs") List<CdpLog> cdpLogs);

    int saveStaff(@Param("industryDBName") String industryDBName,
                  @Param("staffList") List<Staff> staffList);
}
