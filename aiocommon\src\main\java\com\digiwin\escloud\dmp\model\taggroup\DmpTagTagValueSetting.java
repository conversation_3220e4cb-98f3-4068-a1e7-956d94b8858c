package com.digiwin.escloud.dmp.model.taggroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Data
@ApiModel(value = "DmpTagTagValueSetting对象", description = "")
public class DmpTagTagValueSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("标签id")
    private Long tagId;

    @ApiModelProperty("分数来源")
    private String scoreSource;

    @ApiModelProperty("分数")
    private BigDecimal score;

    @ApiModelProperty("操作值")
    private String operatorValue;

    private String leftOperatorValue;

    private String rightOperatorValue;

    private String leftOperator;

    private String rightOperator;

    @ApiModelProperty("权重")
    private BigDecimal weight;

    @ApiModelProperty("序号")
    private Integer orderNum;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
