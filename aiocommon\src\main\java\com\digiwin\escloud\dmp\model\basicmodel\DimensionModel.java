package com.digiwin.escloud.dmp.model.basicmodel;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-04-21 17:19
 * @Description
 */
@Data
// (tableName="dmp_dimension_model",groups = ModifyGroup.class)
public class DimensionModel {
    private long id;
    private Long sid;
    private Long eid;
    private String appCode;
    private long dimensionId;
    private String code;
    private String name;
    private String modelCode;
    private List<Map<String, Object>> keys;
    private String uploadDataModelCode;
    private boolean based;
    private String remark;

    private String baseModelKey;
}
