-- 清理重复用户通知联系人数据的SQL演示脚本

-- 1. 查看当前重复数据的情况
SELECT 
    userId,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY id) as all_ids,
    MAX(id) as keep_id
FROM user_notify_contact 
WHERE userId IS NOT NULL AND userId != ''
GROUP BY userId
HAVING COUNT(*) > 1
ORDER BY userId;

-- 2. 查看需要删除的记录详情
SELECT 
    unc.id,
    unc.userId,
    unc.name,
    unc.email,
    unc.telephone,
    '需要删除' as action
FROM user_notify_contact unc
INNER JOIN (
    SELECT userId, MAX(id) as maxId
    FROM user_notify_contact
    WHERE userId IS NOT NULL AND userId != ''
    GROUP BY userId
    HAVING COUNT(*) > 1
) max_records ON unc.userId = max_records.userId
WHERE unc.userId IS NOT NULL AND unc.userId != ''
AND unc.id != max_records.maxId
ORDER BY unc.userId, unc.id;

-- 3. 查看需要保留的记录详情
SELECT 
    unc.id,
    unc.userId,
    unc.name,
    unc.email,
    unc.telephone,
    '保留' as action
FROM user_notify_contact unc
INNER JOIN (
    SELECT userId, MAX(id) as maxId
    FROM user_notify_contact
    WHERE userId IS NOT NULL AND userId != ''
    GROUP BY userId
    HAVING COUNT(*) > 1
) max_records ON unc.userId = max_records.userId
WHERE unc.id = max_records.maxId
ORDER BY unc.userId;

-- 4. 查看受影响的映射关系
SELECT 
    tngum.tngId,
    tngum.uncId as old_uncId,
    max_records.maxId as new_uncId,
    unc.userId,
    unc.name as old_contact_name,
    keep_unc.name as new_contact_name
FROM tenant_notify_group_user_mapping tngum
INNER JOIN user_notify_contact unc ON tngum.uncId = unc.id
INNER JOIN (
    SELECT userId, MAX(id) as maxId
    FROM user_notify_contact
    WHERE userId IS NOT NULL AND userId != ''
    GROUP BY userId
    HAVING COUNT(*) > 1
) max_records ON unc.userId = max_records.userId
LEFT JOIN user_notify_contact keep_unc ON keep_unc.id = max_records.maxId
WHERE unc.id != max_records.maxId
ORDER BY unc.userId, tngum.tngId;

-- 5. 统计信息查询
SELECT 
    '总记录数' as metric,
    COUNT(*) as count
FROM user_notify_contact
WHERE userId IS NOT NULL AND userId != ''

UNION ALL

SELECT 
    '重复userId数量' as metric,
    COUNT(*) as count
FROM (
    SELECT userId
    FROM user_notify_contact
    WHERE userId IS NOT NULL AND userId != ''
    GROUP BY userId
    HAVING COUNT(*) > 1
) duplicate_users

UNION ALL

SELECT 
    '需要删除的记录数' as metric,
    COUNT(*) as count
FROM user_notify_contact unc
INNER JOIN (
    SELECT userId, MAX(id) as maxId
    FROM user_notify_contact
    WHERE userId IS NOT NULL AND userId != ''
    GROUP BY userId
    HAVING COUNT(*) > 1
) max_records ON unc.userId = max_records.userId
WHERE unc.id != max_records.maxId

UNION ALL

SELECT 
    '受影响的映射关系数' as metric,
    COUNT(*) as count
FROM tenant_notify_group_user_mapping tngum
INNER JOIN user_notify_contact unc ON tngum.uncId = unc.id
INNER JOIN (
    SELECT userId, MAX(id) as maxId
    FROM user_notify_contact
    WHERE userId IS NOT NULL AND userId != ''
    GROUP BY userId
    HAVING COUNT(*) > 1
) max_records ON unc.userId = max_records.userId
WHERE unc.id != max_records.maxId;

-- 6. 验证清理后的数据完整性（清理后执行）
-- SELECT 
--     userId,
--     COUNT(*) as count_after_cleanup
-- FROM user_notify_contact 
-- WHERE userId IS NOT NULL AND userId != ''
-- GROUP BY userId
-- HAVING COUNT(*) > 1;
-- -- 如果清理成功，这个查询应该返回空结果

-- 7. 验证映射关系的完整性（清理后执行）
-- SELECT 
--     tngum.tngId,
--     tngum.uncId,
--     unc.userId,
--     unc.name,
--     CASE 
--         WHEN unc.id IS NULL THEN '映射关系异常：找不到对应的联系人'
--         ELSE '映射关系正常'
--     END as status
-- FROM tenant_notify_group_user_mapping tngum
-- LEFT JOIN user_notify_contact unc ON tngum.uncId = unc.id
-- WHERE unc.id IS NULL OR unc.userId IS NULL OR unc.userId = '';
-- -- 如果清理成功，这个查询应该只返回映射关系正常的记录
