package com.digiwin.escloud.aioitms.networksecurity.controller;

import com.digiwin.escloud.aioitms.bigdata.NetworkSecurityDataParams;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsReportRecord;
import com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamAssetRequest;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamRequest;
import com.digiwin.escloud.aioitms.networksecurity.model.request.UpdateNetworkSecurityExamSimpleReportRequest;
import com.digiwin.escloud.aioitms.networksecurity.service.NetworkSecurityExamService;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/network/security")
public class NetworkSecurityExamController {

    @Resource
    private NetworkSecurityExamService networkSecurityExamService;

    @PostMapping("/getNetworkSecurityExam")
    public ResponseBase getNetworkSecurityExam(@RequestBody NetworkSecurityExamRequest request) {
        return networkSecurityExamService.getNetworkSecurityExam(request);
    }

    @PostMapping("/saveNetworkSecurityExamRecord")
    public BaseResponse saveNetworkSecurityExamRecord(@RequestBody AiopsExamRecord record) {
        return networkSecurityExamService.saveNetworkSecurityExamRecord(record);
    }

    @PostMapping("/saveOrUpdateProjectType")
    public ResponseBase saveOrUpdateProjectType(@RequestBody NetworkSecurityExaminationProjectType projectType) {
        return networkSecurityExamService.saveOrUpdateProjectType(projectType);
    }

    @GetMapping("/getProjectType")
    public ResponseBase getProjectType(
            @RequestParam(name = "categoryCode", required = false) String categoryCode,
            @RequestParam(name = "categoryName", required = false) String categoryName,
            @RequestParam(name = "parentCode", required = false) String parentCode,
            @RequestParam(name = "eid", required = false) Long eid,
            @RequestParam(name = "filterNullModel", required = false, defaultValue = "true") Boolean filterNullModel,
            @RequestParam(name = "aerId", required = false, defaultValue = "0") Long aerId
    ) {
        return networkSecurityExamService.getProjectType(categoryCode, categoryName, parentCode, filterNullModel, aerId, eid);
    }

    @PostMapping("/saveNetworkSecurityAsset")
    public ResponseBase saveNetworkSecurityAsset(
            @RequestBody List<AiopsExamItemInstanceScore> aeiisList,
            @RequestParam("recordId") Long recordId) {
        return networkSecurityExamService.saveNetworkSecurityAsset(aeiisList, recordId);
    }

    @PostMapping("/getNetworkSecurityExamAsset")
    public BaseResponse getNetworkSecurityExamAsset(@RequestBody NetworkSecurityExamAssetRequest request) {
        return networkSecurityExamService.getNetworkSecurityExamAsset(request);
    }

    @DeleteMapping("/deleteNetworkSecurityExamAsset")
    public BaseResponse deleteNetworkSecurityExamAsset(
            @RequestParam("aiopsItemId") String aiopsItemId,
            @RequestParam("recordId") Long recordId) {
        return networkSecurityExamService.deleteNetworkSecurityExamAsset(aiopsItemId, recordId);
    }

    @PostMapping("/saveNetworkSecurityData")
    public BaseResponse saveNetworkSecurityData(@RequestBody NetworkSecurityDataParams networkSecurityDataParams) {
        return networkSecurityExamService.saveNetworkSecurityData(networkSecurityDataParams);
    }

    @DeleteMapping("/deleteNetworkSecurityDataById")
    public BaseResponse deleteNetworkSecurityData(@RequestBody NetworkSecurityDataParams networkSecurityDataParams) {
        return networkSecurityExamService.deleteNetworkSecurityDataById(networkSecurityDataParams);
    }

    @GetMapping("/getAiopsItem")
    public BaseResponse getAiopsItem(
            @RequestParam(name = "modelCode") String modelCode,
            @RequestParam(name = "eid") Long eid
    ) {
        return networkSecurityExamService.getAiopsItem(modelCode, eid);
    }

    @GetMapping("/getProjectInstance")
    public BaseResponse getProjectInstance(
            @RequestParam(name = "aiopsItem") String aiopsItem,
            @RequestParam(name = "eid") Long eid
    ) {
        return networkSecurityExamService.getProjectInstance(aiopsItem, eid);
    }

    @GetMapping("/getNetworkSecurityExamReport/{reportRecordId}")
    public BaseResponse getNetworkSecurityExamReport(@PathVariable Long reportRecordId,
                                                     @RequestParam(value = "scale", defaultValue = "0", required = false) int scale) {
        return networkSecurityExamService.getNetworkSecurityExamReport(reportRecordId, scale);
    }

    @PostMapping("/saveNetworkSecurityExamReport")
    public BaseResponse saveNetworkSecurityExamReport(@RequestBody AiopsExamRecordsReportRecord reportRecord) {
        return networkSecurityExamService.saveNetworkSecurityExamReport(reportRecord);
    }

    @PostMapping("/saveNetworkSecurityExamSimpleReport")
    public BaseResponse saveNetworkSecurityExamSimpleReport(@RequestBody AiopsExamRecordsReportRecord reportRecord,
                                                           @RequestParam(value = "scale", defaultValue = "0", required = false) int scale) {
        return networkSecurityExamService.saveNetworkSecurityExamSimpleReport(reportRecord, scale);
    }

    @GetMapping("/getEnableAeim")
    public ResponseBase getEnableAeim(@RequestParam(required = false,defaultValue = "") String reportType) {
        return networkSecurityExamService.getEnableAeim(reportType);
    }

    @GetMapping("/getNetworkSecurity")
    public ResponseBase getNetworkSecurity(@RequestParam(name = "eid", required = false) Long eid,
                                           @RequestParam(name = "aerId", required = false) Long aerId) {
        return networkSecurityExamService.getNetworkSecurity(eid, aerId);
    }

    @GetMapping("/getNetworkSecurityExamSimpleReport/{id}")
    public BaseResponse getNetworkSecurityExamSimpleReport(@PathVariable String id) {
        return networkSecurityExamService.getNetworkSecurityExamSimpleReport(id);
    }

    @PutMapping("/updateNetworkSecurityExamSimpleReport")
    public BaseResponse updateNetworkSecurityExamSimpleReport(@RequestBody UpdateNetworkSecurityExamSimpleReportRequest request) {
        return networkSecurityExamService.updateNetworkSecurityExamSimpleReport(request.getId(), request.getFieldPath(), request.getValue());
    }

    @GetMapping("/getExamItemInstanceScoreTotal")
    public BaseResponse getExamItemInstanceScoreTotal(@RequestParam(name = "aerId", required = false) Long aerId) {
        return networkSecurityExamService.getExamItemInstanceScoreTotal(aerId);
    }

    @GetMapping("/selectExamRecord")
    public BaseResponse selectExamRecord(@RequestParam(name = "eid", required = false) Long eid,
                                         @RequestParam(value = "scale", defaultValue = "0", required = false) int scale) {
        return networkSecurityExamService.selectExamRecord(eid, scale);
    }

    @ApiOperation(value = "自查表备份成功的数据")
    @GetMapping("/dbBackupFailedDetail")
    public BaseResponse getDbBackupSuccessDetail(@RequestParam String eid,
                                                @RequestParam(required = false) String timeFrom,
                                                @RequestParam(required = false) String timeTo) {
        return networkSecurityExamService.getDbBackupSuccessDetail(eid, timeFrom, timeTo);
    }

}
