package com.digiwin.escloud.dmp.model.basicmodel;

import com.digiwin.escloud.dmp.model.basicmodel.base.BaseModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-04-21 17:17
 * @Description
 */
@Data
// (tableName="dmp_dimension",groups = ModifyGroup.class)
public class Dimension extends BaseModel {
    private long creator;
    private String creatorName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private List<DimensionModel> dimensionModels;
}
