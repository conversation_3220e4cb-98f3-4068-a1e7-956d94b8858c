package com.digiwin.escloud.aiouser.model.tenantNotice;

import lombok.Data;

@Data
public class UserNotifyModuleSettingReq {

    /**
     * 租户id
     */
    private Long eid;
    /**
     * 通知模块id
     */
    private Long nmId;

    /**
     * 是否开启
     */
    private boolean enabled;
    /**
     * 特殊开启，这个参数前端不会使用，是其他地方批量调用使用，目的是覆盖enabled得值
     */
    private Boolean specialEnabled;

    /**
     * 通知方式id
     */
    private String nwIds;

    /**
     * 通知接收级别id
     */
    private String nrlIds;

    /**
     * 预警手机号
     */
    private String telephone;

    /**
     * 预警接收邮箱
     */
    private String email;

    /**
     * 微信openId
     */
    private String wechat;


    /**
     * 获取用户Sid
     */
    private long authUserSid;

    /**
     * 获取用户userId
     */
    private String authUserId;


    /**
     * 用户名
     */
    private String userName;




}
