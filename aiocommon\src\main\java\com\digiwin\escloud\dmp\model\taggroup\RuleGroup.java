package com.digiwin.escloud.dmp.model.taggroup;

import com.digiwin.escloud.dmp.model.condition.AttributeCondition;
import com.digiwin.escloud.dmp.model.condition.EventCondition;
import com.digiwin.escloud.dmp.model.condition.ScriptCondition;
import com.digiwin.escloud.dmp.model.condition.TagCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-04-21 17:59
 * @Description
 */
@Data
public class RuleGroup {
    @ApiModelProperty("分组id")
    private long id;
    @ApiModelProperty("分组类型")
    private RuleGroupType groupType;
    @ApiModelProperty("标签或者分群id")
    private long targetId;
    @ApiModelProperty("分层计算规则")
    private String remark;
    private List<TagLayer> tagLayers;
    private List<AttributeCondition> attributeConditions;
    private List<EventCondition> eventConditions;
    private List<TagCondition> tagConditions;
    private ScriptCondition scriptCondition;
    // 自定义的sql标签 分数计算的设定
    private List<DmpTagTagValueSetting> tagValueSetting;
}
