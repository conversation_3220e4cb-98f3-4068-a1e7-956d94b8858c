package com.digiwin.escloud.dmp.model.condition;

import com.digiwin.escloud.common.model.StatisticsType;
import com.digiwin.escloud.common.model.TimeInterval;
import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.dmp.model.basicmodel.Event;
import com.digiwin.escloud.dmp.model.condition.base.FieldCondition;
import com.digiwin.escloud.dmp.model.taggroup.FieldType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-04-21 17:32
 * @Description
 */
@Data
public class EventCondition extends FieldCondition {
    @ApiModelProperty("前几(天)")
    private int beforeValue;
    @ApiModelProperty("单位")
    private UnitType unit;
    @ApiModelProperty("是否发生")
    private boolean happened;
    @ApiModelProperty("查询目标字段编号")
    private String targetFieldCode;
    @ApiModelProperty("查询目标字段类型")
    private FieldType targetFieldType;
    @ApiModelProperty("事件id")
    private long eventId;
    private Event event;
    @ApiModelProperty("维度属性id")
    private StatisticsType statisticsType;
    @ApiModelProperty("事件条件备注")
    private String eventRemark;
    private TimeInterval timeInterval;
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
    private List<EventConditionDetail> eventConditionDetails;
}
